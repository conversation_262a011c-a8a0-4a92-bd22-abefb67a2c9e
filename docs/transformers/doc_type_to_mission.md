# Transformer : Type vers Mission

## Vue d'ensemble

Le `TypeToMissionTransformer` convertit les types de tâches du système legacy en entités `Mission` dans le nouveau système, structurant les missions métier.

## Responsabilités

- Transformation des types Helios V1 en Missions Helios V2
- Création de missions basées sur typologie métier
- Association avec projets et domaines métier
- Gestion des types de mission appropriés
- Attribution de codes mission uniques

## Mapping des Données

### Transformation

| Champ Source (Type) | Champ Cible (Mission) | Transformation |
|--------------------|----------------------|----------------|
| `libelle` | `libelle` | Normalisation |
| `description` | `description` | Enrichissement |
| `code` | `code` | Génération automatique |

### Logique <PERSON>

- **Classification** : Détermination du type de mission selon contexte
- **Hiérarchie** : Association avec projets parents
- **Statut** : Initialisation selon règles métier
- **Complexité** : Attribution de niveau de complexité

## Utilisation

Utilisé pour créer la structure de missions dans le nouveau système basée sur la typologie métier de l'ancien système.
