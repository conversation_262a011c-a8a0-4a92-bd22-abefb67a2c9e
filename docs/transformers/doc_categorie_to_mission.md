# Transformer : Catégorie vers Mission

## Vue d'ensemble

Le `CategorieToMissionTransformer` convertit les catégories du système legacy en entités Mission dans le nouveau système, structurant la classification métier.

## Responsabilités

- Transformation des catégories Helios V1 en Missions Helios V2
- Recherche et association des types de mission appropriés
- Génération de descriptions pour les missions
- Gestion des doublons via recherche par libellé
- Association avec domaines métier

## Mapping des Données

### Transformation

| Champ Source (Categorie) | Champ Cible (Mission) | Transformation |
|-------------------------|----------------------|----------------|
| `libelle` | `libelle` | Direct ou normalisé |
| `description` | `description` | Via `GetDescriptionForMission()` |

### Logique <PERSON>

- **Recherche préalable** : Vérification d'existence par libellé
- **Type Mission** : Association automatique selon règles métier
- **Description** : Génération basée sur la catégorie source

## Méthodes Utilitaires

- `findMissionByLibelle()` : Recherche de missions existantes
- `findTypeMissionByLibelle()` : Recherche de types de mission
- `GetDescriptionForMission()` : Génération de descriptions

## Validation

- Contrôle de validité des catégories source
- Vérification des contraintes métier
