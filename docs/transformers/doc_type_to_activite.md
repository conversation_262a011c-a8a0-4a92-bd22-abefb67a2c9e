# Transformer : Type vers Activité

## Vue d'ensemble

Le `TypeToActiviteTransformer` convertit les types de tâches du système legacy en entités `Activite` dans le nouveau système, structurant les activités opérationnelles.

## Responsabilités

- Transformation des types Helios V1 en Activités Helios V2
- Création d'activités basées sur typologie opérationnelle
- Association avec missions parentes
- Gestion des types d'activité appropriés
- Attribution de codes activité uniques

## Mapping des Données

### Transformation

| Champ Source (Type) | Champ Cible (Activite) | Transformation |
|--------------------|------------------------|----------------|
| `libelle` | `libelle` | Normalisation |
| `description` | `description` | Enrichissement |
| `code` | `code` | Génération automatique |

### Logique <PERSON>

- **Classification** : Détermination du type d'activité selon contexte
- **Hiérarchie** : Association avec missions parentes
- **Statut** : Initialisation selon règles métier
- **Durée estimée** : Calcul basé sur historique si disponible

## Utilisation

Utilisé pour créer la structure d'activités opérationnelles dans le nouveau système basée sur la typologie de l'ancien système.
