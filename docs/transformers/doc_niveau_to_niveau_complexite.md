# Transformer : Niveau vers Niveau Complexité

## Vue d'ensemble

Le `NiveauToNiveauComplexiteTransformer` convertit les niveaux de difficulté du système legacy en entités `NiveauComplexite` dans le nouveau système, gérant la classification de complexité.

## Responsabilités

- Transformation des niveaux Helios V1 en Niveaux Complexité Helios V2
- Normalisation des échelles de complexité
- Mapping des valeurs numériques vers des libellés
- Association avec les domaines métier appropriés
- Standardisation de la classification

## Mapping des Données

### Transformation

| Champ Source (Niveau) | Champ Cible (NiveauComplexite) | Transformation |
|----------------------|-------------------------------|----------------|
| `valeur` | `niveau` | Normalisation numérique |
| `libelle` | `libelle` | Direct ou généré |
| `description` | `description` | Enrichissement |

### Logique <PERSON>

- **Échelle standardisée** : Conversion vers échelle uniforme
- **Libellés cohérents** : Standardisation des appellations
- **Valeurs numériques** : Normalisation des niveaux

## Utilisation

Utilisé pour classifier la complexité des tâches et issues dans le nouveau système avec une échelle standardisée.
