# Transformer : Pole vers Domaine M<PERSON>tier

## Vue d'ensemble

Le `PoleToDomaineMetierTransformer` convertit les pôles organisationnels du système legacy en entités `DomaineMetier` dans le nouveau système, structurant l'organisation métier.

## Responsabilités

- Transformation des pôles Helios V1 en Domaines Métier Helios V2
- Normalisation des libellés de pôles
- Création d'entités DomaineMetier avec hiérarchie
- Gestion des codes et descriptions métier
- Association avec l'organisation cible

## Mapping des Données

### Transformation

| Champ Source (Pole) | Champ Cible (DomaineMetier) | Transformation |
|--------------------|----------------------------|----------------|
| `libelle` | `libelle` | Normalisation |
| `code` | `code` | Direct ou généré |
| `description` | `description` | Enrichissement |

### Logi<PERSON>

- **Hiérarchisation** : Création de structure organisationnelle
- **Normalisation** : Standardisation des libellés
- **Codes uniques** : Attribution de codes métier

## Utilisation

Utilisé pour structurer l'organisation métier dans le nouveau système et associer les entités aux bons domaines.
