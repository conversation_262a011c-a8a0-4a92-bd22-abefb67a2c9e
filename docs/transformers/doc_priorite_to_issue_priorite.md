# Transformer : Priorité vers Issue Priorité

## Vue d'ensemble

Le `PrioriteToIssuePrioriteTransformer` convertit les priorités textuelles des tickets legacy en entités `IssuePriorite` du nouveau système.

## Responsabilités

- Transformation des priorités de tickets en entités IssuePriorite
- Normalisation des libellés de priorité
- Gestion des priorités par défaut ("Normale" si vide)
- Recherche dans le cache des priorités existantes
- Association avec les domaines métier

## Mapping des Données

### Transformation

| Champ Source | Champ Cible | Transformation |
|--------------|-------------|---------------|
| `ticket.priorite` | `IssuePriorite` | Recherche par libellé |
| Priorité vide | "Normale" | Valeur par défaut |

### Logique <PERSON>tier

- **Normalisation** : Recherche insensible à la casse
- **Défaut** : "Normale" pour les priorités vides ou null
- **Cache** : Utilise le cache pour optimiser les performances

## Utilisation

Utilisé par `TicketsToIssueTransformer` pour associer les priorités aux issues informatiques.

## Gestion d'Erreurs

- Retour `null` si ticket invalide
- Gestion des priorités vides avec valeur par défaut
