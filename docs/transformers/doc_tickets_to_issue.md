# Transformer : Tickets vers Issue Informatique

## Vue d'ensemble

Le `TicketsToIssueTransformer` est le transformer principal qui convertit les tickets du système legacy (SQL Server) en Issues Informatique dans le nouveau système (MySQL). Il s'agit du transformer le plus complexe et critique du processus ETL.

## Responsabilités

- Transformation des tickets Helios V1 en Issues Informatique Helios V2
- Validation des données d'entrée via `TicketValidator`
- Génération de codes uniques pour les nouvelles issues
- Mapping des statuts et priorités
- Gestion des relations avec les entités liées (contrats, utilisateurs, etc.)
- Calcul de l'avancement basé sur le statut
- Traitement des pièces jointes associées

## Mapping des Données

### Champs Principaux

| Champ Source (Tickets) | Champ Cible (IssueInformatique) | Transformation |
|------------------------|----------------------------------|----------------|
| `idTickets` | `code` | Génération d'un code unique |
| `titre` | `sujet` | Direct ou "Issue {id}" si vide |
| `description` | `description` | Direct ou texte par défaut |
| `status` | `statut` | Via `StatusToIssueStatutTransformer` |
| `priorite` | `priorite` | Via `PrioriteToIssuePrioriteTransformer` |
| `dateCreation` | `dateCreation` | Direct |
| `dateResolution` | `dateEffectiveFin` | Si résolu |

### Logique Métier Spéciale

- **Avancement** : 100% si statut "Résolu", 10% sinon
- **Code unique** : Généré automatiquement pour éviter les doublons
- **Dates** : `datePrevisionnelleFin` = `dateEffectiveFin` si résolu

## Dépendances

- `TicketValidator` : Validation des données d'entrée
- `CacheMemory` : Accès aux données en cache
- `HeliosFilesService` : Gestion des pièces jointes
- Transformers liés : Status, Priorité, Utilisateur, etc.

## Gestion d'Erreurs

- Validation préalable avec `TicketValidator.IsTicketValid()`
- Logging détaillé des erreurs de transformation
- Retour de `TransformationResult` avec statut de succès/échec
- Gestion des valeurs nulles et données manquantes

## Points d'Attention

- **Performance** : Transformer le plus lourd du processus
- **Intégrité** : Doit maintenir les relations entre entités
- **Validation** : Contrôles stricts avant transformation
- **Unicité** : Génération de codes uniques obligatoire
