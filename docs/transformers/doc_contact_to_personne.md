# Transformer : Contact vers Personne

## Vue d'ensemble

Le `ContactToPersonneTransformer` convertit les entités Contact du système legacy en entités Personne dans le nouveau système. Il gère la transformation des informations personnelles et professionnelles.

## Responsabilités

- Transformation des contacts Helios V1 en Personnes Helios V2
- Validation des données de contact
- Normalisation des noms et prénoms (majuscules/capitalisation)
- Détermination du type interne/externe basé sur `ctNum`
- Gestion du cache pour éviter les doublons
- Association avec les domaines métier et types de personne

## Mapping des Données

### Champs Principaux

| Champ Source (Contact) | Champ Cible (Personne) | Transformation |
|------------------------|------------------------|----------------|
| `nom` | `nom` | Conversion en majuscules |
| `prenom` | `prenom` | Capitalisation (première lettre) |
| `email` | `email` | Direct |
| `telephone` | `telephone` | Direct |
| `fonction` | `fonction` | Direct |
| `ctNum` | `externe` | `false` si "AB", `true` sinon |

### Logique Métier

- **Type interne/externe** : Déterminé par `ctNum` ("AB" = interne)
- **Capitalisation** : Noms en majuscules, prénoms capitalisés
- **Cache** : Vérification d'existence avant création
- **Source externe** : Traçabilité avec `SourceOfData`

## Validation

- Vérification de la présence des champs obligatoires
- Contrôle de la validité des données avant transformation
- Gestion des valeurs nulles et vides

## Utilisation

Utilisé par `ClientToCommanditaireTransformer` et d'autres transformers nécessitant des informations de personne.
