# Transformer : Type vers Projet

## Vue d'ensemble

Le `TypeToProjetTransformer` convertit les types de tâches du système legacy en entités `Projet` dans le nouveau système, restructurant la gestion de projets.

## Responsabilités

- Transformation des types Helios V1 en Projets Helios V2
- Création de structure projet basée sur typologie
- Association avec commanditaires et domaines métier
- Gestion des hiérarchies de projets
- Attribution de codes projet uniques

## Mapping des Données

### Transformation

| Champ Source (Type) | Champ Cible (Projet) | Transformation |
|--------------------|----------------------|----------------|
| `libelle` | `nom` | Normalisation |
| `description` | `description` | Enrichissement |
| `code` | `code` | Génération automatique |

### Logique <PERSON>

- **Structure projet** : Création de hiérarchie organisationnelle
- **Codes uniques** : Attribution automatique de codes projet
- **Statut** : Détermination du statut initial selon contexte
- **Responsabilités** : Association avec personnes appropriées

## Utilisation

Utilisé pour créer la structure de projets dans le nouveau système basée sur la typologie métier de l'ancien système.
