# Transformer : Tickets Historique vers Journal

## Vue d'ensemble

Le `TicketsHistoriqueToJournalTransformer` convertit l'historique des modifications des tickets legacy en entrées de Journal dans le nouveau système, préservant la traçabilité.

## Responsabilités

- Transformation de l'historique des tickets en entrées Journal
- Gestion des notes internes/externes
- Association avec les issues correspondantes
- Traitement des pièces jointes liées à l'historique
- Préservation de la chronologie des modifications

## Mapping des Données

### Champs Principaux

| Champ Source (TicketsHistorique) | Champ Cible (Journal) | Transformation |
|----------------------------------|----------------------|----------------|
| `dateModification` | `dateCreation` | Direct |
| `description` | `note` | Direct |
| `noteInterne` | `interne` | `true` si > 0 |
| `pieceJointe` | `piecesJointes` | Via transformer PJ |

### Logi<PERSON>

- **Notes internes** : Basées sur le flag `noteInterne`
- **Privé** : Toujours `false` par défaut
- **Pièces jointes** : Traitement conditionnel si présentes
- **Association** : Lien obligatoire avec une issue

## Dépendances

- `PjHeliosToIssuePieceJointeTransformer` : Pour les pièces jointes
- `HeliosFilesService` : Gestion des fichiers attachés

## Validation

- Contrôle de la validité de l'historique (`idHistorique > 0`)
- Validation de l'issue associée
- Gestion des valeurs nulles

## Utilisation

Utilisé pour préserver l'historique complet des modifications apportées aux tickets lors de la migration.
