# Transformer : Utilisateur vers Personne

## Vue d'ensemble

Le `UtilisateurToPersonneTransformer` convertit les entités Utilisateurs du système legacy en entités Personne dans le nouveau système, avec gestion du type interne/externe.

## Responsabilités

- Transformation des utilisateurs Helios V1 en Personnes Helios V2
- Validation des données utilisateur
- Normalisation des noms et prénoms
- Détermination du type interne/externe basé sur la société
- Gestion des fonctions par défaut
- Association avec domaines métier et types de personne

## Mapping des Données

### Champs Principaux

| Champ Source (Utilisateurs) | Champ Cible (Personne) | Transformation |
|-----------------------------|------------------------|----------------|
| `nom` | `nom` | Conversion en majuscules |
| `prenom` | `prenom` | Capitalisation |
| `email` | `email` | Direct |
| `telephone` | `telephone` | Direct |
| `societe` | `externe` | `false` si "ACTUELBURO" |

### Logique Métier

- **Type interne/externe** : Déterminé par société ("ACTUELBURO" = interne)
- **Fonction par défaut** : Assignée selon règles métier
- **Normalisation** : Noms en majuscules, prénoms capitalisés
- **Société** : "N/A" si null

## Validation

- Vérification des champs obligatoires
- Contrôle de validité avant transformation

## Utilisation

Utilisé pour transformer les utilisateurs associés aux tickets en personnes du nouveau système.
