# Transformer : P<PERSON> vers Issue Pièce Jointe

## Vue d'ensemble

Le `PjHeliosToIssuePieceJointeTransformer` convertit les pièces jointes du système legacy en entités `IssuePieceJointe` du nouveau système, gérant les fichiers et métadonnées.

## Responsabilités

- Transformation des pièces jointes Helios V1 vers Helios V2
- Gestion des fichiers physiques et métadonnées
- Association avec les issues ou entrées de journal
- Validation de l'existence des fichiers
- Préservation des informations de taille et type

## Mapping des Données

### Champs Principaux

| Champ Source (HeliosPJ) | Champ Cible (IssuePieceJointe) | Transformation |
|-------------------------|-------------------------------|----------------|
| `nomFichier` | `nomFichier` | Direct |
| `tailleFichier` | `tailleFichier` | Direct |
| `typeFichier` | `typeMime` | Normalisation MIME |
| `cheminFichier` | `cheminPhysique` | Adaptation nouveau système |

### Logique Métier

- **Validation fichier** : Vérification existence physique
- **Type MIME** : Conversion des extensions en types MIME
- **Chemin** : Adaptation à la nouvelle structure de stockage
- **Association** : Lien avec issue ou journal parent

## Dépendances

- `HeliosFilesService` : Accès aux fichiers physiques
- Services de stockage du nouveau système

## Validation

- Contrôle de l'existence du fichier source
- Validation des métadonnées (taille, type)
- Vérification des permissions d'accès

## Gestion d'Erreurs

- Gestion des fichiers manquants
- Logging des erreurs de copie/transformation
- Création d'entrées avec statut d'erreur si nécessaire
