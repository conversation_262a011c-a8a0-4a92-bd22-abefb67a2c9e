# Transformer : Commande vers Document Contractuel

## Vue d'ensemble

Le `CommandeToDocumentContractuelTransformer` convertit les commandes du système legacy en entités `DocumentContractuel` dans le nouveau système, gérant les aspects contractuels et juridiques.

## Responsabilités

- Transformation des commandes Helios V1 en Documents Contractuels Helios V2
- Gestion des références contractuelles
- Association avec les commanditaires appropriés
- Traitement des conditions et termes contractuels
- Préservation des informations légales

## Mapping des Données

### Transformation

| Champ Source (Commande) | Champ Cible (DocumentContractuel) | Transformation |
|------------------------|----------------------------------|----------------|
| `reference` | `reference` | Direct |
| `libelle` | `titre` | Normalisation |
| `dateCommande` | `dateSignature` | Direct |
| `conditions` | `conditions` | Enrichissement |

### Logique <PERSON>

- **Références uniques** : Préservation des identifiants contractuels
- **Statut contractuel** : Détermination automatique selon contexte
- **Validité** : Contrôle des dates et conditions
- **Hiérarchie** : Association avec documents parents si applicable

## Utilisation

Utilisé pour maintenir la traçabilité contractuelle lors de la migration des projets et missions.
