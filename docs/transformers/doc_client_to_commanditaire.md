# Transformer : Client vers Commanditaire

## Vue d'ensemble

Le `ClientToCommanditaireTransformer` convertit les noms de clients (chaînes de caractères) en entités Commanditaire complètes, en récupérant les informations de contact associées.

## Responsabilités

- Transformation des noms de clients en entités Commanditaire
- Récupération des contacts associés via `ctNum`
- Création d'entités Commanditaire avec personnes liées
- Gestion du cache pour optimiser les performances
- Marquage comme entité externe

## Mapping des Données

### Transformation

| Entrée | Sortie | Transformation |
|--------|--------|---------------|
| `clientName` (String) | `Commanditaire` | Nom en majuscules |
| Contacts via `ctNum` | `personnes` | Via `ContactToPersonneTransformer` |

### Logique <PERSON>

- **Normalisation** : Nom du commanditaire en majuscules
- **Externe** : Toujours marqué comme externe (`externe = true`)
- **Personnes associées** : Récupération via les contacts liés
- **Cache** : Vérification d'existence avant création

## Dépendances

- `ContactToPersonneTransformer` : Pour transformer les contacts en personnes
- `CacheMemory` : Accès aux données de contacts

## Gestion d'Erreurs

- Retour `null` si nom de client vide
- Retour `null` si aucune personne valide trouvée
- Validation des contacts avant transformation
