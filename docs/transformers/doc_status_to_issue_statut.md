# Transformer : Status vers Issue Statut

## Vue d'ensemble

Le `StatusToIssueStatutTransformer` convertit les statuts textuels des tickets legacy en entités `IssueStatut` structurées du nouveau système.

## Responsabilités

- Transformation des statuts de tickets en entités IssueStatut
- Normalisation des libellés de statut
- Gestion des statuts par défaut ("Nouveau" si vide)
- Mapping avec les domaines métier appropriés
- Création d'entités Statut sous-jacentes

## Mapping des Données

### Transformation

| Champ Source | Champ Cible | Transformation |
|--------------|-------------|---------------|
| `ticket.status` | `IssueStatut` | Création d'entité complète |
| Statut vide | "Nouveau" | Valeur par défaut |

### Logique <PERSON>

- **Normalisation** : Conversion des statuts textuels en entités
- **Défaut** : "Nouveau" pour les statuts vides ou null
- **Structure** : Création de `Statut` et `IssueStatut` liés

## Utilisation

Utilisé principalement par `TicketsToIssueTransformer` pour associer les statuts aux issues informatiques.

## Gestion d'Erreurs

- Retour `null` si ticket invalide
- Gestion des statuts vides avec valeur par défaut
