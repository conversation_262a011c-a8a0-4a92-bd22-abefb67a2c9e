#!/usr/bin/env python3
"""
MySQL Table Row Counter with JSON Caching
Connects to MySQL database, counts rows in tables starting with 'hls_',
and caches results locally to avoid frequent database queries.
"""

import mysql.connector
import json
import yaml
import os
from datetime import datetime, timedelta
from typing import Dict, Any
import sys

class MySQLTableCounter:
    def __init__(self, config_file: str = "db_settings.yaml", 
                 cache_file: str = "table_counts_cache.json"):
        """
        Initialize the MySQL Table Counter
        
        Args:
            config_file: Path to YAML configuration file
            cache_file: Path to JSON cache file
        """
        self.config_file = config_file
        self.connection_params = self._load_config()
        self.cache_file = cache_file
        self.cache_duration = timedelta(minutes=1)
    
    def _load_config(self) -> Dict[str, Any]:
        """Load database configuration from YAML file"""
        if not os.path.exists(self.config_file):
            raise FileNotFoundError(f"Configuration file '{self.config_file}' not found. "
                                  f"Please create it with your database settings.")
        
        try:
            with open(self.config_file, 'r') as f:
                config = yaml.safe_load(f)
            
            # Validate required fields
            required_fields = ['host', 'user', 'password', 'database']
            mysql_config = config.get('mysql', {})
            
            missing_fields = [field for field in required_fields 
                            if field not in mysql_config]
            if missing_fields:
                raise ValueError(f"Missing required fields in config: {missing_fields}")
            
            return mysql_config
            
        except yaml.YAMLError as e:
            raise ValueError(f"Error parsing YAML config file: {e}")
        except Exception as e:
            raise ValueError(f"Error loading config file: {e}")
    
    def _load_cache(self) -> Dict[str, Any]:
        """Load cached results from JSON file"""
        if not os.path.exists(self.cache_file):
            return {}
        
        try:
            with open(self.cache_file, 'r') as f:
                return json.load(f)
        except (json.JSONDecodeError, IOError) as e:
            print(f"Warning: Could not load cache file: {e}")
            return {}
    
    def _save_cache(self, data: Dict[str, Any]) -> None:
        """Save results to JSON cache file"""
        try:
            with open(self.cache_file, 'w') as f:
                json.dump(data, f, indent=2, default=str)
        except IOError as e:
            print(f"Warning: Could not save cache file: {e}")
    
    def _is_cache_valid(self, cache_data: Dict[str, Any]) -> bool:
        """Check if cached data is still valid (less than 1 minute old)"""
        if not cache_data or 'timestamp' not in cache_data:
            return False
        
        try:
            cache_time = datetime.fromisoformat(cache_data['timestamp'])
            return datetime.now() - cache_time < self.cache_duration
        except (ValueError, TypeError):
            return False
    
    def _get_hls_tables(self, cursor) -> list:
        """Get all table names that start with 'hls_'"""
        cursor.execute("SHOW TABLES LIKE 'hls_%'")
        tables = [row[0] for row in cursor.fetchall()]
        return tables
    
    def _count_table_rows(self, cursor, table_name: str) -> int:
        """Count rows in a specific table"""
        # Use backticks to handle table names with special characters
        query = f"SELECT COUNT(*) FROM `{table_name}`"
        cursor.execute(query)
        return cursor.fetchone()[0]
    
    def get_table_counts(self, force_refresh: bool = False) -> Dict[str, Any]:
        """
        Get row counts for all hls_ tables, using cache if available and valid
        
        Args:
            force_refresh: If True, ignore cache and query database
            
        Returns:
            Dictionary containing table counts and metadata
        """
        # Check cache first (unless force refresh is requested)
        if not force_refresh:
            cache_data = self._load_cache()
            if self._is_cache_valid(cache_data):
                print("Using cached data (less than 1 minute old)")
                return cache_data
        
        print("Querying database for fresh data...")
        
        try:
            # Connect to MySQL database
            connection = mysql.connector.connect(**self.connection_params)
            cursor = connection.cursor()
            
            # Get all tables starting with 'hls_'
            hls_tables = self._get_hls_tables(cursor)
            
            if not hls_tables:
                print("No tables found starting with 'hls_'")
                return {
                    'timestamp': datetime.now().isoformat(),
                    'tables': {},
                    'total_tables': 0,
                    'total_rows': 0
                }
            
            # Count rows for each table
            table_counts = {}
            total_rows = 0
            
            for table in hls_tables:
                try:
                    row_count = self._count_table_rows(cursor, table)
                    table_counts[table] = row_count
                    total_rows += row_count
                    print(f"Table '{table}': {row_count:,} rows")
                except mysql.connector.Error as e:
                    print(f"Error counting rows in table '{table}': {e}")
                    table_counts[table] = "ERROR"
            
            # Prepare result data
            result = {
                'timestamp': datetime.now().isoformat(),
                'database': self.connection_params['database'],
                'tables': table_counts,
                'total_tables': len(hls_tables),
                'total_rows': total_rows
            }
            
            # Save to cache
            self._save_cache(result)
            
            return result
            
        except mysql.connector.Error as e:
            print(f"Database error: {e}")
            return {}
        except Exception as e:
            print(f"Unexpected error: {e}")
            return {}
        finally:
            if 'connection' in locals() and connection.is_connected():
                cursor.close()
                connection.close()
                print("Database connection closed.")
    
    def display_results(self, results: Dict[str, Any]) -> None:
        """Display the results in a formatted way with proper column alignment"""
        if not results:
            print("No results to display.")
            return
        
        print("\n" + "="*80)
        print("MySQL HLS_ TABLE ROW COUNTS")
        print("="*80)
        print(f"Database: {results.get('database', 'Unknown')}")
        print(f"Timestamp: {results.get('timestamp', 'Unknown')}")
        print(f"Total tables: {results.get('total_tables', 0)}")
        print(f"Total rows: {results.get('total_rows', 0):,}")
        print("-"*80)
        
        tables = results.get('tables', {})
        if not tables:
            print("No hls_ tables found.")
            return
        
        # Calculate the maximum table name length for proper alignment
        max_name_length = max(len(table_name) for table_name in tables.keys())
        
        # Ensure minimum column width for readability
        name_col_width = max(max_name_length, 20)
        
        # Sort tables by row count (descending), then by name for ties
        # Handle both integer counts and error strings
        def sort_key(item):
            table_name, row_count = item
            if isinstance(row_count, int):
                return (-row_count, table_name)  # Negative for descending order
            else:
                return (0, table_name)  # Errors go to bottom
        
        sorted_tables = sorted(tables.items(), key=sort_key)
        
        # Print column headers
        print(f"{'TABLE NAME':<{name_col_width}} | {'ROW COUNT':>15}")
        print("-" * name_col_width + "-+-" + "-" * 15)
        
        # Print table data with proper alignment
        for table_name, row_count in sorted_tables:
            if isinstance(row_count, int):
                print(f"{table_name:<{name_col_width}} | {row_count:>15,}")
            else:
                print(f"{table_name:<{name_col_width}} | {str(row_count):>15}")
        
        print("-" * name_col_width + "-+-" + "-" * 15)
        print(f"{'TOTAL':<{name_col_width}} | {results.get('total_rows', 0):>15,}")
        print("="*80)


def main():
    """Main function to run the script"""
    try:
        # Create counter instance (will load config from database_config.yaml)
        counter = MySQLTableCounter()
        
        # Get table counts (will use cache if available and valid)
        results = counter.get_table_counts()
        
        # Display results
        counter.display_results(results)
        
        # Example: Force refresh (ignore cache)
        # results = counter.get_table_counts(force_refresh=True)
        
        # Example: Use custom config file
        # counter = MySQLTableCounter(config_file="my_custom_config.yaml")
        
    except FileNotFoundError as e:
        print(f"Configuration error: {e}")
        print("\nPlease create a 'database_config.yaml' file with the following structure:")
        print("""
mysql:
  host: localhost
  user: your_username
  password: your_password
  database: your_database
  port: 3306  # optional, defaults to 3306
  charset: utf8mb4  # optional
        """)
        sys.exit(1)
    except ValueError as e:
        print(f"Configuration error: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Script error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()