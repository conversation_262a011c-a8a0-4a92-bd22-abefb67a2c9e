package com.helios.etl.services;

import com.helios.etl.model.IssueInformatique;
import com.helios.etl.progress.entities.EtlRun;
import com.helios.etl.progress.services.DatabaseProgressListener;
import com.helios.etl.progress.services.ProgressTrackingService;
import com.helios.etl.services.transformers.TicketsToIssueTransformer;
import com.helios.etl.services.validators.TicketValidator;
import com.helios.etl.source.entities.Tickets;
import com.helios.etl.source.helper.DataOptionsHelper;
import com.helios.etl.source.repositories.TicketHistoriqueRepository;
import com.helios.etl.source.repositories.TicketsRepository;
import com.helios.etl.utils.TransformationResult;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

@Service
public class Tranformer {

    private static final Logger log = LoggerFactory.getLogger(Tranformer.class);

    @Autowired
    private TicketsRepository ticketsRepository;

    @Autowired
    private TicketHistoriqueRepository ticketHistoriqueRepository;

    @Autowired
    private CacheMemory cacheMemory;

    @Autowired
    private ProgressTrackingService progressTrackingService;

    @Autowired
    private HeliosFilesService heliosFilesService;

    @PersistenceContext
    private EntityManager entityManager;

    /**
     * Backward-compatible run method without progress reporting
     */
    public boolean Run() {
        return Run(null);
    }

    /**
     * Run with optional progress listener to report real-time progress
     */
    public boolean Run(ProgressListener listener) {
        return Run(listener, "run-etl");
    }

    /**
     * Run with optional progress listener and command tracking
     */
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public boolean Run(ProgressListener listener, String command) {
        log.info("Running transformer with command: {}", command);

        // Check if there's already a running ETL process
        if (progressTrackingService.isEtlRunning()) {
            log.error("ETL process is already running. Cannot start new process.");
            return false;
        }

        return this.fullTransform(listener, command);
    }

    private boolean fullTransform(ProgressListener listener, String command)
    {
        List<Tickets> tickets = cacheMemory.getTickets() == null
                ? new ArrayList<>()
                : new ArrayList<>(cacheMemory.getTickets());

        int total = tickets.size();

        // Start ETL run tracking
        EtlRun etlRun = progressTrackingService.startEtlRun(command, total);
        log.info("Started ETL run {} with {} tickets", etlRun.getRunId(), total);

        // Create database progress listener that wraps the original listener
        DatabaseProgressListener dbProgressListener = new DatabaseProgressListener(
                progressTrackingService, etlRun.getId(), listener);

        try {
            // Update status to running
            progressTrackingService.updateEtlRunStatus(etlRun.getId(), EtlRun.EtlRunStatus.RUNNING);

            // Initialize and run the central transformer
            TicketsToIssueTransformer t = new TicketsToIssueTransformer(entityManager, heliosFilesService);
            t.set_cm(cacheMemory);

            Set<Integer> alreadyProcessedTickets = alreadyProcessedTickets();
            tickets = tickets.stream()
                    .filter(ticket -> !alreadyProcessedTickets.contains(ticket.getIdTickets()))
                    .toList();

            log.info("After filtering, {} tickets to process", tickets.size());

            int current = 0;
            for (Tickets ticket : tickets) {
                current++;

                try {
                    if(TicketValidator.IsTicketValid(ticket))
                    {
                        // Start tracking this ticket transformation
                        dbProgressListener.startTicketProcessing(ticket.getIdTickets(), current);

                        // Perform the transformation
                        TransformationResult result = t.TransformTicketToIssueInformatique(ticket);
                        if (!result.isSuccess()) {
                            log.warn("Ticket {} transformation failed: {}", ticket.getIdTickets(), result.getErrorMessage());
//                            System.exit(1);
                            dbProgressListener.onTicketTransformationError(ticket.getIdTickets(), result.getErrorMessage(), null);
                            continue; // Skip to next ticket
                        }

                        // Report success
                        dbProgressListener.onProgress(total, current, "OK");
                    } else {
                        // Collect detailed invalidation reasons and persist skip into progress DB
                        List<String> reasons = TicketValidator.GetInvalidReasons(ticket);
                        String reasonText = reasons.isEmpty() ? "Ticket invalid" : String.join("; ", reasons);
                        try {
                            progressTrackingService.skipInvalidTicket(etlRun.getId(), ticket.getIdTickets(), current, reasonText);
                        } catch (Exception e) {
                            log.error("Failed to record skipped ticket {} in progress DB: {}", ticket.getIdTickets(), e.getMessage());
                        }
                        log.warn("Ticket {} have been ignored due to invalid data: {}", ticket.getIdTickets(), reasonText);
                    }
                } catch (Exception e) {
                    log.warn("Ticket transformation failed for id {}: {}", ticket.getIdTickets(), e.getMessage());

                    // Check if this is a Hibernate assertion failure that could corrupt the session
                    if (e.getMessage() != null &&
                            (e.getMessage().contains("null identifier") ||
                                    e.getMessage().contains("Session/EntityManager is closed") ||
                                    e.getMessage().contains("EntityManagerFactory is closed"))) {

                        log.error("Hibernate session corruption detected for ticket {}: {}", ticket.getIdTickets(), e.getMessage());

                        // Don't try to clear if EntityManager is already closed
                        if (!e.getMessage().contains("closed")) {
                            try {
                                // Clear the session to prevent further corruption
                                entityManager.clear();
                                log.info("Successfully cleared corrupted EntityManager session");
                            } catch (Exception clearException) {
                                log.error("Failed to clear EntityManager session: {}", clearException.getMessage());
                            }
                        } else {
                            log.warn("EntityManager is closed, cannot clear session. Continuing with next ticket.");
                        }
                    }

                    // Report error with detailed information
                    dbProgressListener.onTicketTransformationError(ticket.getIdTickets(), e.getMessage(), e);
                    dbProgressListener.onProgress(total, current, "ERR");
                }
            }

            // Complete the ETL run successfully
            progressTrackingService.completeEtlRun(etlRun.getId(), true, null);
            log.info("ETL run {} completed successfully", etlRun.getRunId());
            return true;

        } catch (Exception ex) {
            log.error("Transformer run failed", ex);
            progressTrackingService.completeEtlRun(etlRun.getId(), false, ex.getMessage());
            return false;
        }
    }

    private boolean updateJournaux(ProgressListener listener, String command)
    {
        return false;
    }

    public Set<Integer> alreadyProcessedTickets()
    {
        Set<Integer> alreadyProcessed = new HashSet<>();

        for (IssueInformatique issue : cacheMemory.getIssueInformatiques()) {
            Integer idTicket = DataOptionsHelper.getIdTicketsFromIssueOptions(issue);
            if (idTicket != null) {
                alreadyProcessed.add(idTicket);
            }
        }

        double percent = alreadyProcessed.size() / (double) cacheMemory.getTickets().size() * 100;
        log.info("Already processed {}/{} ({}%) tickets for the BU Informatique", alreadyProcessed.size(), cacheMemory.getTickets().size(), percent);

        return alreadyProcessed;
    }
}
