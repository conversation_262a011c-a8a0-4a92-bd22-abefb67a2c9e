package com.helios.etl.services.validators;

import com.helios.etl.helper.StringHelper;
import com.helios.etl.source.entities.Tickets;
import com.helios.etl.source.helper.DefaultHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;

import static com.helios.etl.helper.StringHelper.sanitizeField;

public class TicketValidator {
    private static final Logger log = LoggerFactory.getLogger(TicketValidator.class);

    /**
     * Returns a list of validation errors. Empty list means the ticket is valid.
     */
    public static List<String> GetInvalidReasons(Tickets ticket) {
        List<String> reasons = new ArrayList<>();
        if (ticket == null) {
            reasons.add("ticket is null");
            return reasons;
        }

        // Validate required ID
        if (ticket.getIdTickets() <= 0) {
            reasons.add("idTickets <= 0");
        }

        if (ticket.getStatus() != null && ticket.getStatus().equalsIgnoreCase("inactif")) {
            reasons.add("status is 'inactif'");
        }

        if ((ticket.getCategorie() == null || ticket.getCategorie().isEmpty()) &&
            (ticket.getCategorie2() == null || ticket.getCategorie2().isEmpty()) &&
            (ticket.getCategorie3() == null || ticket.getCategorie3().isEmpty())) {
            reasons.add("all categories (categorie, categorie2, categorie3) are empty");
        }

        // Validate required string fields are not null or empty
        if (StringHelper.isNullOrEmpty(ticket.getCtNum())) {
            reasons.add("ctNum is null or empty");
        }

        if (StringHelper.isNullOrEmpty(ticket.getClient())) {
            reasons.add("client is null or empty");
        }

        if (StringHelper.isNullOrEmpty(ticket.getTitre())) {
            reasons.add("titre is null or empty");
        }

        // Validate date fields are not DefaultHelper.DEFAULT_MIN_DATE (which indicates uninitialized)
        if (ticket.getDateCreation() == null || ticket.getDateCreation().equals(DefaultHelper.DEFAULT_MIN_DATE)) {
            reasons.add("dateCreation is null or DEFAULT_MIN_DATE");
        }

        // Validate business logic constraints
        if (ticket.getDateCreation() != null &&
            !ticket.getDateCreation().equals(DefaultHelper.DEFAULT_MIN_DATE) &&
            ticket.getDateCreation().isAfter(LocalDateTime.now().plusDays(1))) {
            reasons.add("dateCreation is in the future (> now + 1 day)");
        }

        // Validate resolution date is not before creation date (if set)
        if (ticket.getDateResolution() != null &&
            !ticket.getDateResolution().equals(DefaultHelper.DEFAULT_MIN_DATE) &&
            ticket.getDateCreation() != null &&
            !ticket.getDateCreation().equals(DefaultHelper.DEFAULT_MIN_DATE) &&
            ticket.getDateResolution().isBefore(ticket.getDateCreation())) {
            reasons.add("dateResolution is before dateCreation");
        }

        // Validate niveau is within reasonable range
        if (ticket.getNiveau() < 0 || ticket.getNiveau() > 2) {
            reasons.add("niveau out of range (expected 0..2)");
        }

        // Validate tempsTotal is not negative
        if (ticket.getTempsTotal() < 0) {
            reasons.add("tempsTotal is negative");
        }

        if(!reasons.isEmpty())
        {
            String ticketConcerne = "Ticket " + ticket.getIdTickets();
            reasons.add(ticketConcerne);
        }

        return reasons;
    }

    public static boolean IsTicketValid(Tickets ticket) {
        List<String> reasons = GetInvalidReasons(ticket);
        if (!reasons.isEmpty()) {
            // Log a single warning with all invalid reasons for easier debugging/SQLite logging
            try {
                if (ticket != null) {
                    log.warn("Ticket invalid and will be skipped. id={}, ctNum='{}', reasons=[{}]",
                            ticket.getIdTickets(), ticket.getCtNum(), String.join("; ", reasons));
                } else {
                    log.warn("Ticket invalid and will be skipped. Reasons: {}", String.join("; ", reasons));
                }
            } catch (Exception e) {
                // Defensive logging in case getters throw
                log.warn("Ticket invalid and will be skipped. Reasons: {}", String.join("; ", reasons));
            }
            return false;
        }

        if (StringHelper.isNullOrEmpty(ticket.getStatus())) {
            ticket.setStatus("Non traité");
        }

        SanitizeTicketFields(ticket);

        log.debug("Ticket validation successful for ticket {} with ctNum {}",
                ticket.getIdTickets(), ticket.getCtNum());
        return true;
    }

    public static Tickets SanitizeTicketFields(Tickets ticket) {
        if (ticket == null) {
            return null;
        }

        if(ticket.getStatus().equalsIgnoreCase("Résolu") && (ticket.getDateResolution() == null))
        {
            ticket.setDateResolution(LocalDateTime.now());
        }

        // Define field length constants
        final int CATEGORIE_MAX_LENGTH = 50;
        final int DESCRIPTION_MAX_LENGTH = 5000;
        final int TITRE_MAX_LENGTH = 200;
        final int STATUS_MAX_LENGTH = 50;
        final int POLE_MAX_LENGTH = 50;
        final int TYPE_MAX_LENGTH = 50;
        final int PRIORITE_MAX_LENGTH = 50;
        final int NAME_MAX_LENGTH = 100; // For demandeur, assigne, etc.

        HashSet<String> validStatus = new HashSet<>();
        validStatus.add("Non traité");
        validStatus.add("En cours");
        validStatus.add("Résolu");
        validStatus.add("A planifier");
        validStatus.add("Planifié");

        if(!validStatus.contains(ticket.getStatus()))
        {
            ticket.setStatus("Non traité");
        }

        final String defaultStatus = "Non traité";
        final String defaultType = "Support";
        final String defaultPriorite = "Normale";
        final String defaultDescription = "Imported from Helios V1.";

        // Sanitize all fields using the helper method
        sanitizeField(ticket::getCtNum, ticket::setCtNum, NAME_MAX_LENGTH, null);
        sanitizeField(ticket::getClient, ticket::setClient, NAME_MAX_LENGTH, null);
        sanitizeField(ticket::getDemandeur, ticket::setDemandeur, NAME_MAX_LENGTH, null);
        sanitizeField(ticket::getPole, ticket::setPole, POLE_MAX_LENGTH, null);
        sanitizeField(ticket::getType, ticket::setType, TYPE_MAX_LENGTH, defaultType);
        sanitizeField(ticket::getStatus, ticket::setStatus, STATUS_MAX_LENGTH, defaultStatus);
        sanitizeField(ticket::getPriorite, ticket::setPriorite, PRIORITE_MAX_LENGTH, defaultPriorite);
        sanitizeField(ticket::getCategorie, ticket::setCategorie, CATEGORIE_MAX_LENGTH, null);
        sanitizeField(ticket::getAssigne, ticket::setAssigne, NAME_MAX_LENGTH, null);
        sanitizeField(ticket::getTitre, ticket::setTitre, TITRE_MAX_LENGTH, null);
        sanitizeField(ticket::getDescription, ticket::setDescription, DESCRIPTION_MAX_LENGTH, defaultDescription);
        sanitizeField(ticket::getCategorie2, ticket::setCategorie2, CATEGORIE_MAX_LENGTH, null);
        sanitizeField(ticket::getCategorie3, ticket::setCategorie3, CATEGORIE_MAX_LENGTH, null);
        sanitizeField(ticket::getDernierCorrespondant, ticket::setDernierCorrespondant, NAME_MAX_LENGTH, null);

        return ticket;
    }
}
