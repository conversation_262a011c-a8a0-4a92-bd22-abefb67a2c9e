package com.helios.etl.services.transformers;

import com.helios.etl.model.*;
import com.helios.etl.services.CacheMemory;
import com.helios.etl.services.HeliosFilesService;
import com.helios.etl.services.validators.TicketValidator;
import com.helios.etl.source.entities.*;
import com.helios.etl.utils.TransformationResult;
import jakarta.persistence.EntityManager;
import jakarta.persistence.NoResultException;
import jakarta.persistence.PersistenceContext;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;

public class TicketsToIssueTransformer {
    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(TicketsToIssueTransformer.class);

    @Getter
    @Setter
    private CacheMemory _cm;

    @PersistenceContext
    private EntityManager _em;

    private TransformationResult transformationResult = new TransformationResult();

    private final HeliosFilesService heliosFilesService;

    public TicketsToIssueTransformer(EntityManager em, HeliosFilesService heliosFilesService) {
        this._em = em;
        this.heliosFilesService = heliosFilesService;
    }

    /**
     * Transforme un ticket en issue informatique
     * @param ticket the ticket to transform
     * @return TransformationResult with success status and error message if any
     */
    public TransformationResult TransformTicketToIssueInformatique(Tickets ticket)
    {
        if(!TicketValidator.IsTicketValid(ticket)) {
            return this.getTransformationResultAndLog("Ticket " + ticket.getIdTickets() + " is invalid, skipping", false);
        }

        IssueInformatique issue = this.createBaseIssueInformatique(ticket);

        // Set required fields that were missing
        String uniqueCode = generateUniqueIssueCode(ticket);
        issue.setCode(uniqueCode);
        issue.setSujet(ticket.getTitre() != null && !ticket.getTitre().isEmpty() ? ticket.getTitre() : "Issue " + ticket.getIdTickets());
        issue.setDescription(ticket.getDescription() != null && !ticket.getDescription().isEmpty() ? ticket.getDescription() : "Imported from ticket " + ticket.getIdTickets());
        issue.setKindOfActivite(""); // Will be set after activite is assigned
        issue.setKindOfIssueParente(""); // No parent issue for imported tickets

        Map<String, String> options = new HashMap<String, String>();

        if(ticket.getStatus().equals("Résolu"))
        {
            LocalDateTime resolutionDate = ticket.getDateResolution() != null ? ticket.getDateResolution() : LocalDateTime.now();
            issue.setDateEffectiveFin(resolutionDate);
            issue.setDatePrevisionnelleFin(resolutionDate);
            issue.setAvancement((byte) 100);
        } else {
            issue.setAvancement((byte) 10);
        }

        // Set priority and status before other operations
        String error = setPriorityAndStatus(issue, ticket);
        if (error != null) {
            return this.getTransformationResultAndLog(error, false);
        }

        // Retrieve contracts as documents contractuels
        HashSet<DocumentContractuel> documentsContractuels = transformTicketContrat(ticket);
        if(documentsContractuels != null)
        {
            issue.getDocumentsContractuels().addAll(documentsContractuels);
        }

        // Resolve DomaineMetier and Activite
        error = resolveAndAssignDomaineAndActivite(issue, ticket);
        if (error != null) {
            return this.getTransformationResultAndLog(error, false);
        }

        // Set kindOfActivite after activite is assigned
        if (issue.getActivite() != null) {
            issue.setKindOfActivite(issue.getActivite().getClass().getSimpleName());
        }

        // Resolve persons (assignee and demandeur)
        error = resolveAndAssignPersons(issue, ticket);
        if (error != null) {
            return this.getTransformationResultAndLog(error, false);
        }

        // Resolve commanditaire
        error = resolveAndAssignCommanditaire(issue, ticket);
        if (error != null) {
            return this.getTransformationResultAndLog(error, false);
        }

        // Options
        issue.setOptions(this.SetOptions(options, ticket));

        // Resolve destinataires
        error = linkTicketDestinataires(issue, ticket);
        if (error != null) {
            return this.getTransformationResultAndLog(error, false);
        }

        //TODO Relation entre issue (Installation & SuiteInstallation)
        if(ticket.getType().trim().equalsIgnoreCase("suite installation"))
        {
            //TODO s'assurer que l'issue parente soit process avant && créer la relation
        }

        //TODO NiveauToNiveauComplexite non géré encore

        //Origine
        IssueOrigine origine = getIssueOrigine();
        if(origine == null)
        {
            return this.getTransformationResultAndLog("Failed to get or create IssueOrigine, skipping", false);
        }
        issue.setOrigine(origine);

        // Persist and cache - do this before creating journaux
        try {
            issue = _cm.getIssueInformatiqueRepository().save(issue);
            _cm.getIssueInformatiqueRepository().flush();
            if (issue.getOid() <= 0) {
                throw new RuntimeException("IssueInformatique was saved but has invalid OID: " + issue.getOid());
            }
            _cm.getIssueInformatiques().add(issue);
            System.out.println("Successfully saved IssueInformatique with OID: " + issue.getOid() + " and code: " + issue.getCode() + " for ticket " + ticket.getIdTickets());
        } catch (Exception e) {
            System.err.println("Failed to save IssueInformatique for ticket " + ticket.getIdTickets() + ": " + e.getMessage());
            e.printStackTrace();
            return this.getTransformationResultAndLog("Failed to save IssueInformatique for ticket " + ticket.getIdTickets() + ": " + e.getMessage(), false);
        }

        // Create journaux from ticket historique (after issue is persisted)
        error = createJournaux(issue, ticket);
        if (error != null) {
            return this.getTransformationResultAndLog(error, false);
        }

        // Link commande and attachments if any
        error = linkCommandeAndAttachments(issue, ticket);
        if (error != null) {
            return this.getTransformationResultAndLog(error, false);
        }

        if(issue != null && issue.getOid() > 0)
        {
            this.transformationResult = this.getTransformationResultAndLog(
                    "Ticket " + ticket.getIdTickets() + " transformed to IssueInformatique with ID " + issue.getOid(), true);
        }
        else
        {
            String msg = "Failed to transform Ticket " + ticket.getIdTickets() + " to IssueInformatique";
            this.transformationResult = this.getTransformationResultAndLog(msg, false);
        }

//        try {
//            _em.flush();
//            return this.getTransformationResultAndLog("Ticket " + ticket.getIdTickets() + " transformed to IssueInformatique with ID " + issue.getOid(), true);
//        } catch (Exception e) {
//            return this.getTransformationResultAndLog("Failed to flush IssueInformatique for ticket " + ticket.getIdTickets() + ": " + e.getMessage(), false);
//        }

        return this.getTransformationResultAndLog("Ticket " + ticket.getIdTickets() + " transformed to IssueInformatique with ID " + issue.getOid(), true);
    }

    private String linkTicketDestinataires(IssueInformatique issue, Tickets ticket) {
        if(ticket == null || ticket.getIdTickets() <= 0)
        {
            return null;
        }

        HashSet<Contact> destinataires = _cm.getContactsRepository().getAllContactsFromTicketsDestinataireByIdTickets(ticket.getIdTickets());
        if(destinataires == null || destinataires.isEmpty() || destinataires.size() == 1) {
            return null;
        }

        ContactToPersonneTransformer ctpt = new ContactToPersonneTransformer(_cm);
        HashSet<Personne> personnes = ctpt.transformContactsToPersonnes(destinataires);
        if(personnes == null || personnes.isEmpty())
        {
            return null;
        }

        personnes.remove(issue.getDemandeur());

        issue.getObservateurs().addAll(personnes);
        return null;
    }

    private IssueInformatique createBaseIssueInformatique(Tickets ticket) {
        IssueInformatique issue = new IssueInformatique();
        TicketsHistorique lastEdit = _cm.getTicketHistoriqueRepository().getLastHistoriqueFromTicketId(ticket.getIdTickets());
        LocalDateTime last = lastEdit == null ? ticket.getDateCreation() : lastEdit.getDateModification();

        issue.setDateCreation(ticket.getDateCreation());
        issue.setDateModification(last);
        issue.setTempsEstimeMinutes(0);
        issue.setTempsEffectifMinutes(ticket.getTempsTotal());

        return issue;
    }

    private IssueOrigine getIssueOrigine() {
        IssueOrigine origine = null;

        String libelle = "HeliosV1";

        //Try retrieve from cache || DB
        if (_cm.getIssueOrigines() != null) {
            origine = _cm.getIssueOrigines().stream()
                    .filter(o -> o.getLibelle().equalsIgnoreCase(libelle))
                    .findFirst()
                    .orElse(null);
        }
        if (origine == null) {
            try {
                origine = _cm.getIssueOrigineRepository().findByLibelle(libelle).orElse(null);
            } catch (Exception e) {
                System.err.println("Failed to retrieve IssueOrigine from DB: " + e.getMessage());
            }
        }
        if(origine == null) {
            origine = new IssueOrigine();
            origine.setLibelle(libelle);
            origine.setDescription("Imported from Helios V1");
            String code = "HELIOS_V1_" + System.currentTimeMillis() % 100000;
            origine.setCode(code);
            DomaineMetier dm = findDomaineMetierByLibelle("Informatique");
            if(dm == null)
            {
                dm = new DomaineMetier();
                dm.setLibelle("Informatique");
                dm.setDescription("Domaine informatique");
                dm.setCode("HELIOS_V1_INFORMATIQUE");
                dm = _cm.getDomaineMetierRepository().save(dm);
                _cm.getDomaineMetiers().add(dm);
            }
            HashSet<DomaineMetier> domaines = new HashSet<>();
            domaines.add(dm);
            origine.setDomainesMetier(domaines);
        }

        if(origine.getOid() <= 0)
        {
            try {
                origine = _cm.getIssueOrigineRepository().save(origine);
                _cm.getIssueOrigines().add(origine);
            } catch (Exception e) {
                System.err.println("Failed to save IssueOrigine: " + e.getMessage());
            }
        }

        return origine;
    }

    /**
     * Resolve and assign domaine metier and activite
     * @param issue the parent issue
     * @param ticket the source ticket
     * @return null if success, error message otherwise
     */
    private String resolveAndAssignDomaineAndActivite(IssueInformatique issue, Tickets ticket) {
        //Pole to domaine metier
        PoleToDomaineMetierTransformer ptdmt = new PoleToDomaineMetierTransformer(_cm);
        DomaineMetier dm = ptdmt.transform(ticket.getPole());
        if(dm == null)
        {
            return "Failed to transform Pole of ticket " + ticket.getIdTickets() + " to DomaineMetier, skipping";
        }

        //POLE & Type to Mission(Activite)
        if(ticket.getType().trim().equalsIgnoreCase("installation"))
        {
            TypeToProjetTransformer ptpt = new TypeToProjetTransformer(_cm);
            Projet type = ptpt.transform(ticket.getPole(), ticket.getType());
            if(type == null)
            {
                return "Failed to transform Type of ticket " + ticket.getIdTickets() + " to Projet, skipping";
            }
            type.setDomainePrincipal(dm);
            issue.setActivite(type);
        } else {
            TypeToMissionTransformer ptmt = new TypeToMissionTransformer(_cm);
            Mission type = ptmt.transform(ticket.getPole(), ticket.getType());
            if(type == null)
            {
                return "Failed to transform Type of ticket " + ticket.getIdTickets() + " to Mission, skipping";
            }
            type.setDomainePrincipal(dm);
            issue.setActivite(type);
        }
        return null;
    }

    /**
     * Resolve and assign persons (assignee and demandeur), Assigne become IntervenantPrincipal and Redacteur, Demandeur become Demandeur
     * @param issue the parent issue
     * @param ticket the source ticket
     * @return null if success, error message otherwise
     */
    private String resolveAndAssignPersons(IssueInformatique issue, Tickets ticket) {
        // Assignee
        Utilisateurs assigne = _cm.getHelper().tryGetUtilisateurByUsername(ticket.getAssigne());
        if(assigne == null)
        {
            assigne = _cm.getUtilisateursRepository().getByUsername(ticket.getAssigne());
            if(assigne == null)
            {
                return "Failed to find or create Utilisateur for assignee " + ticket.getAssigne() + " in ticket " + ticket.getIdTickets();
            }
        }
        UtilisateurToPersonneTransformer utpt = new UtilisateurToPersonneTransformer(_cm);
        Personne p = utpt.transform(assigne);
        if(p == null)
        {
            return "Failed to transform Utilisateur " + ticket.getAssigne() + " to Personne for ticket " + ticket.getIdTickets();
        }
        issue.setIntervenantPrincipal(p);
        issue.getIntervenants().add(p);
        issue.setRedacteur(p);

        // Demandeur
        Contact demandeur = _cm.getContactsRepository().getByFullName(ticket.getDemandeur());
        if(demandeur == null)
        {
            return "Failed to find Contact for demandeur " + ticket.getDemandeur() + " in ticket " + ticket.getIdTickets();
        }
        ContactToPersonneTransformer ctpt = new ContactToPersonneTransformer(_cm);
        Personne demandeurPersonne = ctpt.transform(demandeur);
        if(demandeurPersonne == null)
        {
            return "Failed to transform Contact " + ticket.getDemandeur() + " to Personne for ticket " + ticket.getIdTickets();
        }
        issue.setDemandeur(demandeurPersonne);
        return null;
    }

    /**
     * Resolve (transform) and assign commanditaire
     * @param issue the parent issue
     * @param ticket the source ticket
     * @return null if success, error message otherwise
     */
    private String resolveAndAssignCommanditaire(IssueInformatique issue, Tickets ticket) {
        ClientToCommanditaireTransformer ctct = new ClientToCommanditaireTransformer(_cm);
        Commanditaire commanditaire = ctct.transform(ticket.getCtNum());
        if(commanditaire == null)
        {
            return "Failed to transform Client " + ticket.getCtNum() + " to Commanditaire for ticket " + ticket.getIdTickets();
        }
        issue.setCommanditaire(commanditaire);
        return null;
    }

    /**
     * Create journaux from ticket historique (issue must already be persisted)
     * @param issue the parent issue of the journal (must be persisted with valid OID)
     * @param ticket the ticket to transform
     * @return null if success, error message otherwise
     */
    private String createJournaux(IssueInformatique issue, Tickets ticket) {
        // Ticket historiques
        HashSet<TicketsHistorique> historiques = _cm.getTicketHistoriqueRepository().getAllHistoriqueFromTicketId(ticket.getIdTickets());
        if(historiques == null || historiques.isEmpty())
        {
            return "No TicketHistorique found for ticket " + ticket.getIdTickets() + ", skipping Journal creation";
        }

        // Verify issue is persisted
        if (issue.getOid() <= 0) {
            return "Issue must be persisted before creating journaux for ticket " + ticket.getIdTickets();
        }

        TicketsHistoriqueToJournalTransformer thjt = new TicketsHistoriqueToJournalTransformer(_cm, heliosFilesService);
        HashSet<Journal> journaux = thjt.transformCollection(historiques, issue);
        if(journaux.size() != historiques.size())
        {
            System.out.println("Failed to transform all TicketHistorique for ticket " + ticket.getIdTickets() + " to Journal, skipping");

            return "Failed to transform all TicketHistorique for ticket " + ticket.getIdTickets() + " to Journal, skipping";
        }
        return null;
    }

    private HashSet<DocumentContractuel> transformTicketContrat(Tickets ticket) {
        if (ticket == null) {
            return null;
        }

        List<TicketsContrat> ticketContrat = _cm.getTicketsContratRepository().getByTicketId(ticket.getIdTickets());
        if(ticketContrat == null || ticketContrat.isEmpty())
        {
            return null;
        }
        HashSet<DocumentContractuel> docs = new HashSet<>();
        for (TicketsContrat tc : ticketContrat) {
            TicketContratToDocumentContractuel tcdct = new TicketContratToDocumentContractuel(_cm);
            DocumentContractuel doc = tcdct.transform(tc, ticket);
            if(doc != null)
            {
                docs.add(doc);
            }
        }

        return docs;
    }

    /**
     * Transform commande & PJ commande en Document contractuel & PJ Commande
     * @param issue Correspond à l'issue qui sera utilisé pour lié les PJs.
     * @param ticket Correspond au ticket qui est en cours de transformation.
     * @return null si pas de commande ou OK, sinon message d'erreur.
     */
    private String linkCommandeAndAttachments(IssueInformatique issue, Tickets ticket) {
        Commandes cmd = this.getCommande(ticket);
        if(cmd == null)
        {
            return null;
        }

        CommandeToDocumentContractuelTransformer ctdct = new CommandeToDocumentContractuelTransformer(_cm);
        DocumentContractuel doc = ctdct.transform(cmd);
        if(doc == null)
        {
            return "Failed to transform Commande for ticket " + ticket.getIdTickets() + " to DocumentContractuel, skipping";
        }

        //ADD PIECE JOINTE FROM THE COMMANDE
        try {
            List<HeliosPJ> pjs = heliosFilesService.getCommandePieceJointes(cmd.getCommande());
            if(pjs.size() > 0)
            {
                // Save the DocumentContractuel ONCE before processing all PJs
                if (doc.getOid() <= 0) {
                    doc = _cm.getDocumentContractuelRepository().save(doc);
                    _cm.getDocumentContractuels().add(doc);
                    if (doc.getOid() <= 0) {
                        return "DocumentContractuel was saved but has invalid OID: " + doc.getOid();
                    }
                }

                //ADD PJ TO ISSUEPIECEJOINTE
                HashSet<DocumentContractuelPieceJointe> pjsDoc = new HashSet<>();
                PjHeliosToIssuePieceJointeTransformer pjTransformer = new PjHeliosToIssuePieceJointeTransformer(_cm);
                int nbPJ = 1;
                for(HeliosPJ pj : pjs)
                {
                    pj.setCommandes(cmd);
                    pj.setNumero(nbPJ);
                    DocumentContractuelPieceJointe pjIssue = pjTransformer.transform(pj, doc);
                    if(pjIssue != null)
                    {
                        pjsDoc.add(pjIssue);
                        nbPJ++;
                    }
                }
                if(!pjsDoc.isEmpty())
                {
                    doc.setPiecesJointes(pjsDoc);
                }
            }
        } catch (Exception e) {
            String errorMsg = "Failed to get PieceJointe for commande " + cmd.getCommande() +
                    " for ticket " + ticket.getIdTickets() + " : " + e.getMessage();
            log.warn(errorMsg, e);
            return errorMsg;

        }

        // Save the DocumentContractuel to ensure it has a valid OID
        try {
            doc = _cm.getDocumentContractuelRepository().save(doc);
            _cm.getDocumentContractuels().add(doc);
            if (doc.getOid() <= 0) {
                throw new RuntimeException("DocumentContractuel was saved but has invalid OID: " + doc.getOid());
            }
        } catch (Exception e) {
            return "Failed to save DocumentContractuel for ticket " + ticket.getIdTickets() + ": " + e.getMessage();
        }

        HashSet<DocumentContractuel> docs = new HashSet<>();
        docs.add(doc);
        issue.getDocumentsContractuels().addAll(docs);

        return null;
    }

    /**
     * [HELPER] Log the message and return the transformation result
     * @param msg Message to log
     * @param success Success status
     * @return TransformationResult with the message and success status
     */
    private TransformationResult getTransformationResultAndLog(String msg, boolean success) {
        this.transformationResult.setSuccess(success);
        this.transformationResult.setErrorMessage(msg);
        if (success) {
            log.info(msg);
        } else {
            log.warn(msg);
        }
        return transformationResult;
    }

    /**
     * Set les options l'issue - Notamment les catégories
     * @param options
     * @param ticket
     * @return
     */
    private Map<String, String> SetOptions(Map<String, String> options, Tickets ticket)
    {
        if(options == null)
        {
            options = new HashMap<>();
        }

        options.putAll(ticket.toMap());

//        if(ticket.getIdTickets() > 0)
//        {
//            options.put("helios1.idTicket", String.valueOf(ticket.getIdTickets()));
//        }
//        if(ticket.getCategorie() != null && !ticket.getCategorie().isEmpty())
//        {
//            options.put("helios1.categorie", ticket.getCategorie());
//        }
//        if(ticket.getCategorie2() != null && !ticket.getCategorie2().isEmpty())
//        {
//            options.put("helios1.categorie2", ticket.getCategorie2());
//        }
//        if(ticket.getCategorie3() != null && !ticket.getCategorie3().isEmpty())
//        {
//            options.put("helios1.categorie3", ticket.getCategorie3());
//        }

        return options;
    }

    private DomaineMetier findDomaineMetierByLibelle(String libelle) {
        try {
            return _em.createQuery(
                            "SELECT dm FROM DomaineMetier dm WHERE dm.libelle = :libelle", DomaineMetier.class)
                    .setParameter("libelle", libelle)
                    .getSingleResult();
        } catch (NoResultException e) {
            return null; // No DomaineMetier found with this libelle
        }
    }

    /**
     * Check si le ticket est associée à une commande pour transformation.
     * @param ticket
     * @return
     */
    private Commandes getCommande(Tickets ticket)
    {
        for(Commandes commande : _cm.getCommandes())
        {
            if(commande.getIdTickets() == ticket.getIdTickets())
            {
                return commande;
            }
        }

        return null;
    }

    /**
     * Generate a unique code for IssueInformatique based on ticket information
     * @param ticket the source ticket
     * @return a unique code string
     */
    private String generateUniqueIssueCode(Tickets ticket) {
        // Generate base code using ticket ID and type
        String type = ticket.getType() != null ? ticket.getType().replaceAll("[^a-zA-Z0-9]", "").toUpperCase() : "ISSUE";
        String baseCode = "ISS_" + type + "_" + ticket.getIdTickets();

        // Ensure it doesn't exceed 50 characters
        if (baseCode.length() > 45) {
            baseCode = baseCode.substring(0, 45);
        }

        // Check if this code already exists and add a suffix if needed
        String finalCode = baseCode;
        int counter = 1;
        while (issueCodeExists(finalCode)) {
            finalCode = baseCode + "_" + counter;
            counter++;
            // Ensure we don't exceed 50 chars with the counter
            if (finalCode.length() > 50) {
                baseCode = baseCode.substring(0, 47 - String.valueOf(counter).length());
                finalCode = baseCode + "_" + counter;
            }
            // Safety check to prevent infinite loop
            if (counter > 1000) {
                finalCode = "ISS_" + type + "_" + (System.currentTimeMillis() % 100000);
                break;
            }
        }

        return finalCode;
    }

    /**
     * Check if a code already exists in the IssueInformatique cache or database
     * @param code the code to check
     * @return true if the code exists, false otherwise
     */
    private boolean issueCodeExists(String code) {
        // Check in-memory cache only to avoid triggering a flush during generation
        boolean existsInCache = _cm.getIssueInformatiques().stream()
                .anyMatch(issue -> code.equals(issue.getCode()));

        if (existsInCache) {
            return true;
        }

        // Do not hit the database here to prevent unintended session flushes that can
        // cascade assertion failures if other entities are in an invalid transient state.
        // Rely on cache; uniqueness will still be guaranteed by DB constraints at save time.
        return false;
    }

    /**
     * Set default priority and status for the issue based on ticket status
     * @param issue the issue to set priority and status for
     * @param ticket the source ticket
     * @return null if success, error message otherwise
     */
    private String setPriorityAndStatus(IssueInformatique issue, Tickets ticket) {
        // Set default priority (find the first available or create a default one)
        IssuePriorite priorite = _cm.getIssuePriorites().stream()
                .filter(p -> p.getLibelle().toLowerCase().contains("normale") ||
                           p.getLibelle().toLowerCase().contains("basse") ||
                           p.getLibelle().toLowerCase().contains("urgente") ||
                           p.getLibelle().toLowerCase().contains("elevée"))
                .findFirst()
                .orElse(_cm.getIssuePriorites().stream().findFirst().orElse(null));

        if(priorite == null)
        {
            PrioriteToIssuePrioriteTransformer ptipt = new PrioriteToIssuePrioriteTransformer(_em, _cm);
            priorite = ptipt.transform(ticket);
            if(priorite == null)
            {
                return "Failed to transform Priorite of ticket " + ticket.getIdTickets() + " to IssuePriorite, skipping";
            }
            issue.setPriorite(priorite);
        }
        issue.setPriorite(priorite);

        // Set status based on ticket status
        IssueStatut issueStatut = null;
        String ticketStatus = ticket.getStatus();

        if ("Résolu".equalsIgnoreCase(ticketStatus)) {
            // Look for closed/resolved status
            issueStatut = _cm.getIssueStatuts().stream()
                    .filter(s -> s.isFerme() ||
                               s.getLibelle().toLowerCase().contains("résolu") ||
                               s.getLibelle().toLowerCase().contains("fermé") ||
                               s.getLibelle().toLowerCase().contains("terminé"))
                    .findFirst()
                    .orElse(null);
        } else if ("En cours".equalsIgnoreCase(ticketStatus)) {
            // Look for in-progress status
            issueStatut = _cm.getIssueStatuts().stream()
                    .filter(s -> !s.isNouveau() && !s.isFerme() &&
                               (s.getLibelle().toLowerCase().contains("cours") ||
                                s.getLibelle().toLowerCase().contains("progress") ||
                                s.getLibelle().toLowerCase().contains("actif")))
                    .findFirst()
                    .orElse(null);
        } else {
            // Default to new/open status for other cases
            issueStatut = _cm.getIssueStatuts().stream()
                    .filter(s -> s.isNouveau() ||
                               s.getLibelle().toLowerCase().contains("nouveau") ||
                               s.getLibelle().toLowerCase().contains("ouvert") ||
                               s.getLibelle().toLowerCase().contains("traité"))
                    .findFirst()
                    .orElse(null);
        }

        // Fallback to first available status if no match found
        if (issueStatut == null) {
            issueStatut = _cm.getIssueStatuts().stream().findFirst().orElse(null);
        }

        if (issueStatut == null) {
            StatusToIssueStatutTransformer stist = new StatusToIssueStatutTransformer(_em, _cm);
            issueStatut = stist.transform(ticket);
            if(issueStatut == null)
            {
                return "Failed to transform Status of ticket " + ticket.getIdTickets() + " to IssueStatut, skipping";
            }
        }
        issue.setStatut(issueStatut);

        return null;
    }

}
