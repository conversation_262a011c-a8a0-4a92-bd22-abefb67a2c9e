package com.helios.etl.services.transformers;

import com.helios.etl.model.AbstractIssue;
import com.helios.etl.model.IssueInformatique;
import com.helios.etl.model.IssuePieceJointe;
import com.helios.etl.model.Journal;
import com.helios.etl.services.CacheMemory;
import com.helios.etl.services.HeliosFilesService;
import com.helios.etl.source.entities.HeliosPJ;
import com.helios.etl.source.entities.Tickets;
import com.helios.etl.source.entities.TicketsHistorique;
import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class TicketsHistoriqueToJournalTransformer {
    @Getter
    @Setter
    private CacheMemory _cm;

    private final HeliosFilesService heliosFilesService;

    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(TicketsHistoriqueToJournalTransformer.class);

    public TicketsHistoriqueToJournalTransformer(CacheMemory cm, HeliosFilesService hfs) {
        this._cm = cm;
        this.heliosFilesService = hfs;
    }

    public Journal transform(TicketsHistorique historique, AbstractIssue issue) {
        if (historique == null || historique.getIdHistorique() == 0 || issue == null) {
            return null;
        }

        Journal journal = new Journal();
        journal.setDateCreation(historique.getDateModification());
        journal.setNote(historique.getDescription());
        journal.setIssue(issue);
        journal.setInterne(historique.getNoteInterne() > 0);
        journal.setPrive(false);
        //TODO Aucune identification de l'auteur du journal (correspondant dans helios V1)

        try {
            journal = _cm.getJournalRepository().save(journal);

            if(historique.getPieceJointe() > 0 && journal.getOid() > 0)
            {
                HashSet<IssuePieceJointe> pjs = linkTicketAttachments((IssueInformatique) issue, journal, historique);
                journal.setPiecesJointes(pjs);
                _cm.getJournalRepository().save(journal);
            }

            _cm.getJournaux().add(journal);
        } catch (Exception e) {
            System.err.println("Failed to save Journal for ticket historique " + historique.getIdHistorique() + ": " + e.getMessage());
            e.printStackTrace();
            return null;
        }

        return journal;
    }

    public HashSet<Journal> transformCollection(HashSet<TicketsHistorique> historiques, AbstractIssue issue) {
        if (issue == null) {
            return new HashSet<>();
        }
        if (historiques == null || historiques.isEmpty()) {
            return new HashSet<>();
        }

        HashSet<Journal> journaux = new HashSet<>();
        for(TicketsHistorique th : historiques)
        {
            Journal j = transform(th, issue);
            if(j != null)
            {
                journaux.add(j);
            }
        }

        return journaux;
    }

    /**
     * Transform HeliosPJ en IssuePieceJointe
     * @param issue Correspond à l'issue qui sera utilisé pour lié les PJs.
     * @param ticketsHistorique Correspond au ticketsHistorique qui est en cours de transformation.
     * @return null si pas de PJ ou OK, sinon message d'erreur.
     */
    private HashSet<IssuePieceJointe> linkTicketAttachments(IssueInformatique issue, Journal journal, TicketsHistorique ticketsHistorique) {
        if(ticketsHistorique == null || issue == null)
        {
            return null;
        }

        PjHeliosToIssuePieceJointeTransformer pjTransformer = new PjHeliosToIssuePieceJointeTransformer(_cm);
        String idToTstring = String.valueOf(ticketsHistorique.getIdHistorique());

        Tickets ticket = _cm.getHelper().tryGetTicketById(ticketsHistorique.getIdTickets());
        if(ticket == null)
        {
            ticket = _cm.getTicketsRepository().getById(ticketsHistorique.getIdTickets());
        }

        HashSet<IssuePieceJointe> pjsIssue = new HashSet<>();

        try {
            List<HeliosPJ> pjs = heliosFilesService.getDocumentPieceJointes(idToTstring);
            if(pjs.size() > 0)
            {
                for(HeliosPJ pj : pjs)
                {
                    pj.setTickets(ticket);
                    pj.setNumero(ticketsHistorique.getIdHistorique());
                    IssuePieceJointe pjIssue = pjTransformer.transform(pj, issue, journal);
                    if(pjIssue != null && pjIssue.getOid() > 0)
                    {
                        pjsIssue.add(pjIssue);
                    }
                }
            }
            return pjsIssue;
        } catch (Exception e) {
            String errorMsg = "Failed to get PieceJointe for ticketsHistorique " + ticketsHistorique.getIdTickets() + " : " + e.getMessage();
            log.warn(errorMsg, e);
            return null;
        }
    }
}
