package com.helios.etl.services.transformers;

import com.helios.etl.model.DocumentContractuel;
import com.helios.etl.outer.model.SourceOfData;
import com.helios.etl.services.CacheMemory;
import com.helios.etl.source.entities.Commandes;
import lombok.Getter;
import lombok.Setter;
import org.springframework.cglib.core.Local;

import java.time.LocalDateTime;

public class CommandeToDocumentContractuelTransformer {
    @Getter
    @Setter
    private CacheMemory _cm;

    public CommandeToDocumentContractuelTransformer(CacheMemory cm) {
        this._cm = cm;
    }

    public DocumentContractuel transform (Commandes commande)
    {
        if(!validateCommande(commande))
        {
            return null;
        }

        DocumentContractuel documentContractuel = tryGetDocumentContractuelFromCache(commande);
        if(documentContractuel != null)
        {
            return documentContractuel;
        }

        documentContractuel = new DocumentContractuel();
        documentContractuel.setLibelle(commande.getCommande());
        documentContractuel.setDateCreation(commande.getDateDemandePlanif());
        documentContractuel.setDateModification(commande.getDateLivraison());
        documentContractuel.setDateDebutValidite(commande.getDateDemandePlanif());
        documentContractuel.setDateFinValidite(commande.getDateFin());
        documentContractuel.setDescription(getDescription(commande));

        //TODO TypeDocument non lié au document contractuel pour l'instant ?

        // Set required ExternalEntity fields
        documentContractuel.setExterne(true);

        // Initialize external source information
        SourceOfData externalSource = new SourceOfData();
        externalSource.setOid((long) commande.getIdCommandes());
        externalSource.setKind("Commande");
        externalSource.setName("Helios1_Commande");
        externalSource.setDateUpdated(LocalDateTime.now());
        documentContractuel.setExternalSource(externalSource);

        return documentContractuel;
    }

    private String getDescription(Commandes commande) {
        StringBuilder description = new StringBuilder();
        description.append("Description construite automatiquement à partir des informations Helios V1: ").append(System.lineSeparator());

        description.append(commande.toString());

        return description.toString().trim();
    }

    private DocumentContractuel tryGetDocumentContractuelFromCache(Commandes commande)
    {
        if(_cm == null || _cm.getDocumentContractuelRepository() == null)
            return null;

        DocumentContractuel dc = null;

        for(DocumentContractuel doc : _cm.getDocumentContractuelRepository().findAll())
        {
            if(doc.getLibelle().equalsIgnoreCase(commande.getCommande()))
            {
                dc = doc;
                break;
            }
        }

        return dc;
    }

    private boolean validateCommande(Commandes commande) {
        return commande != null && commande.getIdCommandes() > 0 && commande.getCommande() != null && !commande.getCommande().isEmpty();
    }
}
