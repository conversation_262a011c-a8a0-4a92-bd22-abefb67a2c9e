package com.helios.etl.services.transformers;

import com.helios.etl.helper.DescriptionHelper;
import com.helios.etl.model.DomaineMetier;
import com.helios.etl.model.Mission;
import com.helios.etl.model.TypeMission;
import com.helios.etl.services.CacheMemory;
import lombok.Getter;
import lombok.Setter;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

public class TypeToMissionTransformer {
    @Getter
    @Setter
    private CacheMemory _cm;

    public TypeToMissionTransformer(CacheMemory cm) {
        this._cm = cm;
    }

    public Mission transform(String pole, String type) {
        Mission mission = TryGetExistingMission(pole, type);
        if(mission != null)
        {
            return mission;
        }

        mission = new Mission();

        TypeMission typemission = _cm.getHelper().tryGetTypeMissionFromPoleAndType(pole, type);
        if (typemission == null) {
            // Try to find existing TypeMission by libelle in DB first to avoid duplicates
            try {
                typemission = _cm.getTypeMissionRepository().findByLibelleIgnoreCase(type).orElse(null);
            } catch (Exception e) {
                System.err.println("Warning: findByLibelleIgnoreCase failed: " + e.getMessage());
            }
        }
        if (typemission == null) {
            typemission = new TypeMission();
            // Ensure non-empty fields
            String code = generateUniqueCode(pole, type, "MISSION");
            if (code == null || code.trim().isEmpty()) {
                code = "MISSION_UNKNOWN_" + System.currentTimeMillis();
            }
            typemission.setCode(code);
            typemission.setLibelle(type != null && !type.isBlank() ? type : "Inconnu");
            typemission.setDescription(DescriptionHelper.GetTicketTypeDescription(type));

            try {
                // If a TypeMission with the same code already exists, reuse it
                TypeMission byCode = _cm.getTypeMissionRepository().findByCode(code).orElse(null);
                if (byCode != null) {
                    typemission = byCode;
                } else {
                    typemission = _cm.getTypeMissionRepository().save(typemission);
                }
                // Add to cache if missing
                final TypeMission typemissionRef = typemission;
                if (_cm.getTypeMissions().stream().noneMatch(tm -> tm.getOid() == typemissionRef.getOid())) {
                    _cm.getTypeMissions().add(typemissionRef);
                }

                // Associate the TypeMission with the appropriate DomaineMetier
                associateTypeMissionWithDomaine(typemission, pole);

            } catch (Exception e) {
                // Log the error and try to find an existing one with the same libelle/code
                System.err.println("Failed to save TypeMission with code " + code + ": " + e.getMessage());
                try {
                    typemission = _cm.getTypeMissionRepository().findByLibelleIgnoreCase(type).orElse(null);
                    if (typemission == null) {
                        typemission = _cm.getTypeMissionRepository().findByCode(code).orElse(null);
                    }
                } catch (Exception ex) {
                    System.err.println("Warning: repository lookup after save failure also failed: " + ex.getMessage());
                }
                if (typemission == null) {
                    // Try cache as last resort
                    typemission = _cm.getTypeMissions().stream()
                        .filter(tm -> type != null && type.equalsIgnoreCase(tm.getLibelle()))
                        .findFirst()
                        .orElse(null);
                }
                if (typemission == null) {
                    throw new RuntimeException("Failed to create or find TypeMission for type: " + type, e);
                }
            }
        }


        String defaultDescriptionMission = "[Description par defaut] " + typemission.getDescription();
        // Set a unique code for the Mission to avoid constraint violations
        String missionCode = generateUniqueMissionCode(pole, type);
        mission.setCode(missionCode);
        mission.setLibelle(type);
        mission.setDescription(defaultDescriptionMission);
        mission.setType(typemission);

        // Get the DomaineMetier from the pole - this is required for domainePrincipal
        PoleToDomaineMetierTransformer ptdmt = new PoleToDomaineMetierTransformer(_cm);
        DomaineMetier domainePrincipal = ptdmt.transform(pole);

        if (domainePrincipal == null) {
            throw new RuntimeException("Failed to get or create DomaineMetier for pole: " + pole + ". Cannot create Mission without domainePrincipal.");
        }

        // Set the domainePrincipal (required field)
        mission.setDomainePrincipal(domainePrincipal);

        // Try to get additional domains from TypeMission, but domainePrincipal is the minimum requirement
        Set<DomaineMetier> domaines = new HashSet<>();
        // Always include the principal domain
        if (domainePrincipal != null) {
            domaines.add(domainePrincipal);
        }

        try {
            if (typemission.getDomainesMetier() != null && !typemission.getDomainesMetier().isEmpty()) {
                // Add domains ensuring uniqueness by OID to avoid duplicate join-table inserts
                for (DomaineMetier dm : typemission.getDomainesMetier()) {
                    boolean exists = domaines.stream().anyMatch(x -> x != null && dm != null && x.getOid() == dm.getOid());
                    if (!exists) {
                        domaines.add(dm);
                    }
                }
            }
        } catch (Exception e) {
            // Handle lazy loading exception gracefully
            System.err.println("Warning: Could not access domainesMetier for TypeMission " + typemission.getLibelle() + ": " + e.getMessage());
            // Continue with just the principal domain
        }
        mission.setDomaines(domaines);
        mission.setDateCreation(LocalDateTime.now());
        mission.setDateModification(LocalDateTime.now());

        try {
            mission = _cm.getMissionRepository().save(mission);
            _cm.getMissions().add(mission);
        } catch (Exception e) {
            // Log the error and try to find an existing one
            System.err.println("Failed to save Mission with code " + missionCode + ": " + e.getMessage());
            // Try to find existing Mission by type and pole as fallback
            mission = TryGetExistingMission(pole, type);
            if (mission == null) {
                throw new RuntimeException("Failed to create or find Mission for pole: " + pole + ", type: " + type, e);
            }
        }

        return mission;
    }

    private Mission TryGetExistingMission(String pole, String type)
    {
        for(Mission m : _cm.getMissions())
        {
            if(Objects.equals(m.getType().getLibelle(), type) &&
                (
                    Objects.equals(m.getDomainePrincipal().getLibelle(), pole) ||
                    _cm.getHelper().HasDomaineMetierFromPole(m.getDomaines(), pole)
                ))
            {
                return m;
            }
        }

        return null;
    }

    /**
     * Generate a unique code for TypeMission based on pole and type
     * @param pole the pole name
     * @param type the type name
     * @param prefix the prefix to use
     * @return a unique code string
     */
    private String generateUniqueCode(String pole, String type, String prefix) {
        // Clean and normalize the inputs
        String cleanPole = pole != null ? pole.replaceAll("[^a-zA-Z0-9]", "").toUpperCase() : "UNKNOWN";
        String cleanType = type != null ? type.replaceAll("[^a-zA-Z0-9]", "").toUpperCase() : "UNKNOWN";

        // Limit length to avoid exceeding database column constraints (50 chars max)
        if (cleanPole.length() > 10) cleanPole = cleanPole.substring(0, 10);
        if (cleanType.length() > 15) cleanType = cleanType.substring(0, 15);

        // Generate base code
        String baseCode = prefix + "_" + cleanPole + "_" + cleanType;

        // Ensure it doesn't exceed 50 characters
        if (baseCode.length() > 45) {
            baseCode = baseCode.substring(0, 45);
        }

        // Check if this code already exists and add a suffix if needed
        String finalCode = baseCode;
        int counter = 1;
        while (codeExists(finalCode)) {
            finalCode = baseCode + "_" + counter;
            counter++;
            // Ensure we don't exceed 50 chars with the counter
            if (finalCode.length() > 50) {
                baseCode = baseCode.substring(0, 47 - String.valueOf(counter).length());
                finalCode = baseCode + "_" + counter;
            }
        }

        return finalCode;
    }

    /**
     * Check if a code already exists in the TypeMission cache
     * @param code the code to check
     * @return true if the code exists, false otherwise
     */
    private boolean codeExists(String code) {
        return _cm.getTypeMissions().stream()
                .anyMatch(tm -> code.equals(tm.getCode()));
    }

    /**
     * Generate a unique code for Mission based on pole and type
     * @param pole the pole name
     * @param type the type name
     * @return a unique code string
     */
    private String generateUniqueMissionCode(String pole, String type) {
        // Clean and normalize the inputs
        String cleanPole = pole != null ? pole.replaceAll("[^a-zA-Z0-9]", "").toUpperCase() : "UNKNOWN";
        String cleanType = type != null ? type.replaceAll("[^a-zA-Z0-9]", "").toUpperCase() : "UNKNOWN";

        // Limit length to avoid exceeding database column constraints (50 chars max)
        if (cleanPole.length() > 10) cleanPole = cleanPole.substring(0, 10);
        if (cleanType.length() > 15) cleanType = cleanType.substring(0, 15);

        // Generate base code
        String baseCode = "M_" + cleanPole + "_" + cleanType;

        // Ensure it doesn't exceed 50 characters
        if (baseCode.length() > 45) {
            baseCode = baseCode.substring(0, 45);
        }

        // Check if this code already exists and add a suffix if needed
        String finalCode = baseCode;
        int counter = 1;
        while (missionCodeExists(finalCode)) {
            finalCode = baseCode + "_" + counter;
            counter++;
            // Ensure we don't exceed 50 chars with the counter
            if (finalCode.length() > 50) {
                baseCode = baseCode.substring(0, 47 - String.valueOf(counter).length());
                finalCode = baseCode + "_" + counter;
            }
        }

        return finalCode;
    }

    /**
     * Check if a code already exists in the Mission cache
     * @param code the code to check
     * @return true if the code exists, false otherwise
     */
    private boolean missionCodeExists(String code) {
        return _cm.getMissions().stream()
                .anyMatch(m -> code.equals(m.getCode()));
    }

    /**
     * Associate a TypeMission with the appropriate DomaineMetier based on pole
     * @param typeMission the TypeMission to associate
     * @param pole the pole name to find the corresponding DomaineMetier
     */
    private void associateTypeMissionWithDomaine(TypeMission typeMission, String pole) {
        try {
            // Get or create the DomaineMetier for this pole
            PoleToDomaineMetierTransformer ptdmt = new PoleToDomaineMetierTransformer(_cm);
            DomaineMetier domaineMetier = ptdmt.transform(pole);

            if (domaineMetier != null) {
                // Initialize the collection if it's null
                if (typeMission.getDomainesMetier() == null) {
                    typeMission.setDomainesMetier(new ArrayList<>());
                }

                // Add the domain if it's not already present
                if (!typeMission.getDomainesMetier().contains(domaineMetier)) {
                    typeMission.getDomainesMetier().add(domaineMetier);
                    // Save the updated TypeMission
                    _cm.getTypeMissionRepository().save(typeMission);
                    System.out.println("Associated TypeMission " + typeMission.getLibelle() + " with DomaineMetier " + domaineMetier.getLibelle());
                }
            } else {
                System.err.println("Warning: Could not get or create DomaineMetier for pole: " + pole);
            }
        } catch (Exception e) {
            System.err.println("Warning: Failed to associate TypeMission with DomaineMetier for pole " + pole + ": " + e.getMessage());
        }
    }
}
