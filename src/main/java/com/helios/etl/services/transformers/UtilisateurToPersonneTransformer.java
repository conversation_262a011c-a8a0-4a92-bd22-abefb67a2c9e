package com.helios.etl.services.transformers;

import com.helios.etl.model.DomaineMetier;
import com.helios.etl.model.Personne;
import com.helios.etl.model.TypePersonne;
import com.helios.etl.outer.model.SourceOfData;
import com.helios.etl.services.CacheMemory;
import com.helios.etl.source.entities.Utilisateurs;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Map;

public class UtilisateurToPersonneTransformer {
    private CacheMemory _cm = null;

    public UtilisateurToPersonneTransformer(CacheMemory cm) {
        this._cm = cm;
    }

    @Transactional
    public Personne transform(Utilisateurs utilisateur) {
        if (!validateUtilisateur(utilisateur)) {
            return null;
        }

        Personne personne = tryGetPersonneFromCache(utilisateur);

        if (personne != null) {
            return personne;
        }

        personne = new Personne();
        personne.setNom(utilisateur.getNom().toUpperCase());

        String capitalizedPrenom = utilisateur.getPrenom().substring(0, 1).toUpperCase() + utilisateur.getPrenom().substring(1).toLowerCase();
        personne.setPrenom(capitalizedPrenom);
        personne.setEmail(utilisateur.getEmail());
        personne.setTelephone(utilisateur.getTelephone());

        String defaultFonction = getDefaultFonction(utilisateur);
        personne.setFonction(defaultFonction);

        if (utilisateur.getSociete() == null) {
            utilisateur.setSociete("N/A");
        }

        boolean isInterne = utilisateur.getSociete().trim().equalsIgnoreCase("ACTUELBURO");

        // Set required ExternalEntity fields
        personne.setExterne(true); // This is imported from external source

        // Initialize external source information
        SourceOfData externalSource = new SourceOfData();
        externalSource.setOid((long) utilisateur.getIdUtilisateur());
        externalSource.setKind("Utilisateur");
        externalSource.setName("Helios1_Utilisateur");
        externalSource.setDateUpdated(LocalDateTime.now());
        personne.setExternalSource(externalSource);

        HashSet<DomaineMetier> domaineMetiers = getDomainesMetier(utilisateur);

        String service = utilisateur.getService() != null ? utilisateur.getService() : "UNKNOWN";
        String cleanService = service.replaceAll("[^a-zA-Z0-9]", "").toUpperCase();
        if (cleanService.length() > 15) cleanService = cleanService.substring(0, 15);
        String expectedCode = "USER_" + cleanService + "_" + (isInterne ? "INT" : "EXT");

        TypePersonne type = tryGetTypePersonneFromCacheByCode(expectedCode);

        // If a cached TypePersonne is present, ensure it actually exists in DB; otherwise resolve by code or create
        if (type != null && type.getOid() > 0) {
            try {
                if (_cm.getTypePersonneRepository().findOneByOid(type.getOid()).isEmpty()) {
                    // The referenced OID does not exist in DB, try to resolve by code
                    TypePersonne dbType = _cm.getTypePersonneRepository().findByCode(expectedCode).orElse(null);
                    if (dbType != null) {
                        type = dbType;
                        if (_cm.getTypePersonnes() != null) _cm.getTypePersonnes().add(type);
                    } else {
                        // Force recreation
                        type = null;
                    }
                }
            } catch (Exception e) {
                System.err.println("Warning: Could not verify TypePersonne existence by OID: " + e.getMessage());
            }
        }

        if(type == null) {
            // Try to find existing in database first
            try {
                type = _cm.getTypePersonneRepository().findByCode(expectedCode).orElse(null);
                if (type != null) {
                    // Add to cache for future use
                    if (_cm.getTypePersonnes() != null) {
                        _cm.getTypePersonnes().add(type);
                    }
                    System.out.println("Found existing TypePersonne with code: " + expectedCode);
                }
            } catch (Exception e) {
                System.err.println("Warning: Could not search for existing TypePersonne: " + e.getMessage());
            }
        }

        boolean toSaveType = false;
        if(type == null) {
            // Only create new if it doesn't exist
            type = new TypePersonne();
            type.setCode(expectedCode);
            type.setLibelle(service);
            type.setInterne(isInterne);

            if(!domaineMetiers.isEmpty()) {
                type.setDomainesMetier(domaineMetiers);
            }

            toSaveType = true;
        }

        if(type.getDomainesMetier() == null) {
            type.setDomainesMetier(new HashSet<>());
        }

        int beforeSize = type.getDomainesMetier().size();
        for (DomaineMetier dmNew : domaineMetiers) {
            boolean exists = type.getDomainesMetier().stream()
                    .anyMatch(dmExisting -> dmExisting != null && dmExisting.getOid() == dmNew.getOid());
            if (!exists) {
                type.getDomainesMetier().add(dmNew);
            }
        }
        if (type.getDomainesMetier().size() != beforeSize) {
            toSaveType = true;
        }

        if(toSaveType) {
            try {
                type = _cm.getTypePersonneRepository().save(type);
                // Remove explicit flush - let Spring handle transaction boundaries
                // _cm.getTypePersonneRepository().flush();
                
                if (type.getOid() <= 0) {
                    throw new RuntimeException("TypePersonne was saved but has invalid OID: " + type.getOid());
                }
                if (_cm.getTypePersonnes() != null) {
                    _cm.getTypePersonnes().add(type);
                }
                System.out.println("Successfully saved TypePersonne with OID: " + type.getOid() + " and code: " + type.getCode());
            } catch (org.springframework.dao.DataIntegrityViolationException e) {
                // Handle duplicate key constraint specifically
                System.err.println("TypePersonne with code " + type.getCode() + " already exists, attempting to fetch existing one");
                try {
                    type = _cm.getTypePersonneRepository().findByCode(expectedCode)
                            .orElse(null);
                    if (type != null && _cm.getTypePersonnes() != null && !_cm.getTypePersonnes().contains(type)) {
                        _cm.getTypePersonnes().add(type);
                        System.out.println("Using existing TypePersonne with OID: " + type.getOid());
                    } else if (type == null) {
                        throw new RuntimeException("Could not find existing TypePersonne after duplicate constraint violation");
                    }
                } catch (Exception fetchEx) {
                    throw new RuntimeException("Failed to fetch existing TypePersonne after duplicate error", fetchEx);
                }
            }
        }

        Map<String,String> jsonData = utilisateur.toMap();
        personne.setData(jsonData);

        // Validate TypePersonne has valid OID before assignment
        if (type == null) {
            throw new RuntimeException("TypePersonne is null for user " + utilisateur.getNom() + " " + utilisateur.getPrenom());
        }
        if (type.getOid() <= 0) {
            throw new RuntimeException("TypePersonne has invalid OID (" + type.getOid() + ") for user " + utilisateur.getNom() + " " + utilisateur.getPrenom());
        }

        personne.setType(type);

        try {
            // Validate all required fields before attempting save
            validatePersonneBeforeSave(personne, utilisateur);

            personne = _cm.getPersonneRepository().save(personne);
            // Do not force flush here; let transaction boundaries handle persistence to avoid flushing unrelated entities
            // Verify the Personne was saved with a valid OID
            if (personne.getOid() <= 0) {
                throw new RuntimeException("Personne was saved but has invalid OID: " + personne.getOid());
            }
            if (_cm.getPersonnes() != null) {
                _cm.getPersonnes().add(personne);
            }
            System.out.println("Successfully saved Personne with OID: " + personne.getOid() + " for user " + utilisateur.getNom() + " " + utilisateur.getPrenom());
        } catch (Exception e) {
            System.err.println("Failed to save Personne for user " + utilisateur.getNom() + " " + utilisateur.getPrenom() + ": " + e.getMessage());
            System.err.println("TypePersonne OID: " + (type != null ? type.getOid() : "null"));
            System.err.println("Personne details: nom=" + personne.getNom() + ", prenom=" + personne.getPrenom() + ", email=" + personne.getEmail());
            e.printStackTrace();

            // Don't throw exception immediately - try to find existing Personne as fallback
            personne = findExistingPersonne(utilisateur);
            if (personne == null) {
                throw new RuntimeException("Failed to create or find Personne for user " + utilisateur.getNom() + " " + utilisateur.getPrenom(), e);
            }
            System.out.println("Using existing Personne with OID: " + personne.getOid());
        }

        return personne;
    }


    private TypePersonne tryGetTypePersonneFromCache(Utilisateurs utilisateur, boolean isInterne) {
        if (_cm == null || _cm.getTypePersonnes() == null || utilisateur.getService() == null) {
            return null;
        }

        for (TypePersonne type : _cm.getTypePersonnes()) {
            if (type.getLibelle() != null &&
                    type.getLibelle().equalsIgnoreCase(utilisateur.getService()) &&
                    type.isInterne() == isInterne) {
                return type;
            }
        }

        return null;
    }

    private TypePersonne tryGetTypePersonneFromCacheByCode(String code) {
        if (_cm == null || _cm.getTypePersonnes() == null || code == null) {
            return null;
        }
        for (TypePersonne type : _cm.getTypePersonnes()) {
            if (code.equals(type.getCode())) {
                return type;
            }
        }
        return null;
    }

    private boolean validateUtilisateur(Utilisateurs utilisateur) {
        if(utilisateur == null)
            return false;

        if(utilisateur.getNom() == null || utilisateur.getNom().isEmpty())
            return false;

        if(utilisateur.getPrenom() == null || utilisateur.getPrenom().isEmpty())
            return false;

        if(utilisateur.getTerrain() == null)
            utilisateur.setTerrain(false);

        if(utilisateur.getService() == null)
            utilisateur.setService("Non renseigné");

        if(utilisateur.getSociete() == null)
            utilisateur.setSociete("Non renseigné");

        if(utilisateur.getMultiPole() == null)
            utilisateur.setMultiPole("");

        if(utilisateur.getGroupe() == null)
            utilisateur.setGroupe(0);

        if(utilisateur.getIdPole() == null)
            utilisateur.setIdPole(0);

        if(utilisateur.getAdmin() == null)
            utilisateur.setAdmin(false);

        if(utilisateur.getTelephone() == null)
            utilisateur.setTelephone("");

        if(utilisateur.getEmail() == null)
            utilisateur.setEmail("");

        return true;
    }

    private Personne tryGetPersonneFromCache(Utilisateurs utilisateur) {
        if (_cm == null || _cm.getPersonnes() == null) {
            return null;
        }

        for (Personne p : _cm.getPersonnes()) {
            if (p.getNom().equalsIgnoreCase(utilisateur.getNom()) &&
                p.getPrenom().equalsIgnoreCase(utilisateur.getPrenom())) {
                return p;
            }
        }

        return null;
    }


    private HashSet<DomaineMetier> getDomainesMetier(Utilisateurs utilisateur) {
        HashSet<DomaineMetier> domainesMetiers = new HashSet<>();
        PoleToDomaineMetierTransformer ptdmt = new PoleToDomaineMetierTransformer(_cm);

        // Add null check for getMultiPole()
        if (utilisateur.getMultiPole() != null) {
            String multiPole = utilisateur.getMultiPole().trim().toLowerCase();

            DomaineMetier d = switch (multiPole) {
                case "informatique" -> ptdmt.transform("Informatique");
                case "telecom" -> ptdmt.transform("Telecom");
                case "logiciel" -> ptdmt.transform("Logiciel");
                case "bureautique" -> ptdmt.transform("Bureautique");
                default -> null;
            };

            if (d != null) {
                domainesMetiers.add(d);
            }
        }

        if(domainesMetiers.isEmpty()) {
            DomaineMetier d = ptdmt.transform(utilisateur.getService());
            if (d != null) {
                domainesMetiers.add(d);
            }
        }

        return domainesMetiers;
    }

    private String getDefaultFonction(Utilisateurs u)
    {
        boolean isTerrain = u.getTerrain();
        String defaultFonction = u.getService();
        if(isTerrain)
        {
            defaultFonction += " (Terrain)";
        }

        if(defaultFonction == null || defaultFonction.isEmpty())
        {
            defaultFonction = "Non renseigné";
        }

        return defaultFonction;
    }

    /**
     * Generate a unique code for TypePersonne based on type, service and interne flag
     * @param typePrefix the type prefix (e.g., "USER", "CONTACT")
     * @param service the service name
     * @param isInterne whether the person is internal
     * @return a unique code string
     */
    private String generateUniqueTypePersonneCode(String typePrefix, String service, boolean isInterne) {
        // Clean and normalize the service name
        String cleanService = service != null ? service.replaceAll("[^a-zA-Z0-9]", "").toUpperCase() : "UNKNOWN";

        // Limit length to avoid exceeding database column constraints (50 chars max)
        if (cleanService.length() > 15) cleanService = cleanService.substring(0, 15);

        // Generate base code with more room for counter
        String baseCode = typePrefix + "_" + cleanService + "_" + (isInterne ? "INT" : "EXT");

        // Ensure it doesn't exceed 40 characters to leave room for counter
        if (baseCode.length() > 40) {
            baseCode = baseCode.substring(0, 40);
        }

        // Check if this code already exists and add a suffix if needed
        String finalCode = baseCode;
        int counter = 1;
        while (typePersonneCodeExists(finalCode)) {
            finalCode = baseCode + "_" + counter;
            counter++;
            // Ensure we don't exceed 50 chars with the counter
            if (finalCode.length() > 50) {
                // Reduce base code length to accommodate larger counter
                int maxBaseLength = 50 - String.valueOf(counter).length() - 1; // -1 for underscore
                if (maxBaseLength > 0) {
                    baseCode = baseCode.substring(0, Math.min(baseCode.length(), maxBaseLength));
                    finalCode = baseCode + "_" + counter;
                } else {
                    // If we can't fit, use a timestamp-based approach
                    finalCode = typePrefix + "_" + System.currentTimeMillis() % 100000;
                    break;
                }
            }

            // Safety check to prevent infinite loop
            if (counter > 1000) {
                // Use timestamp as last resort
                finalCode = typePrefix + "_" + System.currentTimeMillis() % 100000;
                break;
            }
        }

        return finalCode;
    }

    /**
     * Check if a code already exists in the TypePersonne cache or database
     * @param code the code to check
     * @return true if the code exists, false otherwise
     */
    private boolean typePersonneCodeExists(String code) {
        // Check in-memory cache first
        boolean existsInCache = _cm.getTypePersonnes().stream()
                .anyMatch(tp -> code.equals(tp.getCode()));

        if (existsInCache) {
            return true;
        }

        // Also check the database using the repository
        try {
            return _cm.getTypePersonneRepository().existsByCode(code);
        } catch (Exception e) {
            System.err.println("Warning: Could not check database for existing TypePersonne code: " + e.getMessage());
            return false;
        }
    }

    /**
     * Validate Personne entity before attempting to save
     * @param personne the Personne to validate
     * @param utilisateur the source Utilisateur
     * @throws RuntimeException if validation fails
     */
    private void validatePersonneBeforeSave(Personne personne, Utilisateurs utilisateur) {
        if (personne.getNom() == null || personne.getNom().trim().isEmpty()) {
            throw new RuntimeException("Personne nom is null or empty for user " + utilisateur.getIdUtilisateur());
        }
        if (personne.getPrenom() == null || personne.getPrenom().trim().isEmpty()) {
            throw new RuntimeException("Personne prenom is null or empty for user " + utilisateur.getIdUtilisateur());
        }
        if (personne.getEmail() == null || personne.getEmail().trim().isEmpty()) {
            throw new RuntimeException("Personne email is null or empty for user " + utilisateur.getIdUtilisateur());
        }
        if (personne.getTelephone() == null) {
            personne.setTelephone(""); // Set empty string instead of null
        }
        if (personne.getFonction() == null || personne.getFonction().trim().isEmpty()) {
            throw new RuntimeException("Personne fonction is null or empty for user " + utilisateur.getIdUtilisateur());
        }
        if (personne.getType() == null) {
            throw new RuntimeException("Personne type is null for user " + utilisateur.getIdUtilisateur());
        }
        if (personne.getType().getOid() <= 0) {
            throw new RuntimeException("Personne type has invalid OID for user " + utilisateur.getIdUtilisateur());
        }
        if (personne.getExternalSource() == null) {
            throw new RuntimeException("Personne externalSource is null for user " + utilisateur.getIdUtilisateur());
        }
    }

    /**
     * Try to find an existing Personne for the given user
     * @param utilisateur the source Utilisateur
     * @return existing Personne or null if not found
     */
    private Personne findExistingPersonne(Utilisateurs utilisateur) {
        try {
            return _cm.getPersonnes().stream()
                .filter(p -> p.getExternalSource() != null &&
                           p.getExternalSource().getOid() != null &&
                           p.getExternalSource().getOid().equals((long) utilisateur.getIdUtilisateur()) &&
                           "Utilisateur".equals(p.getExternalSource().getKind()))
                .findFirst()
                .orElse(null);
        } catch (Exception e) {
            System.err.println("Warning: Could not search for existing Personne: " + e.getMessage());
            return null;
        }
    }
}