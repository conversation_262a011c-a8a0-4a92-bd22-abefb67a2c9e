package com.helios.etl.services.transformers;

import com.helios.etl.model.DocumentContractuel;
import com.helios.etl.outer.model.SourceOfData;
import com.helios.etl.services.CacheMemory;
import com.helios.etl.source.entities.Tickets;
import com.helios.etl.source.entities.TicketsContrat;
import com.helios.etl.source.helper.DataOptionsHelper;
import com.helios.etl.source.helper.DefaultHelper;
import lombok.Getter;
import lombok.Setter;
import org.springframework.cglib.core.Local;

import java.time.LocalDateTime;

public class TicketContratToDocumentContractuel {
    @Getter
    @Setter
    private CacheMemory _cm;

    public TicketContratToDocumentContractuel(CacheMemory cm) {
        this._cm = cm;
    }

    public DocumentContractuel transform (TicketsContrat ticketContrat, Tickets ticket)
    {
        if(!validateTicketContrat(ticketContrat))
        {
            return null;
        }

        LocalDateTime creationDate = ticket != null && ticket.getDateCreation() != null ? ticket.getDateCreation() : DefaultHelper.DEFAULT_MIN_DATE;
        LocalDateTime modificationDate = ticket != null && ticket.getDateCreation() != null ? ticket.getDateCreation() : DefaultHelper.DEFAULT_MIN_DATE;
        LocalDateTime dateFin = ticket != null && ticket.getDateResolution() != null ? ticket.getDateResolution() : DefaultHelper.DEFAULT_MIN_DATE;

        DocumentContractuel documentContractuel = tryGetDocumentContractuelFromCache(ticketContrat);
        if(documentContractuel != null)
        {
            return documentContractuel;
        }

        documentContractuel = new DocumentContractuel();
        documentContractuel.setLibelle(ticketContrat.getLibelle());
        documentContractuel.setDateCreation(creationDate);
        documentContractuel.setDateModification(modificationDate);
        documentContractuel.setDateDebutValidite(dateFin);
        documentContractuel.setDateFinValidite(dateFin);
        documentContractuel.setDescription(ticketContrat.toString());

        // Set required ExternalEntity fields
        documentContractuel.setExterne(true);

        // Initialize external source information
        SourceOfData externalSource = new SourceOfData();
        externalSource.setOid((long) ticketContrat.getIdTicketsContrat());
        externalSource.setKind("TicketsContrat");
        externalSource.setName("Helios1_TicketsContrat");
        externalSource.setDateUpdated(LocalDateTime.now());
        documentContractuel.setExternalSource(externalSource);

        try {
            documentContractuel = _cm.getDocumentContractuelRepository().save(documentContractuel);
            _cm.getDocumentContractuels().add(documentContractuel);
        } catch (Exception e) {
            System.err.println("Failed to save DocumentContractuel: " + e.getMessage());
            return null;
        }

        return documentContractuel;
    }

    private DocumentContractuel tryGetDocumentContractuelFromCache(TicketsContrat ticketContrat) {
        for(DocumentContractuel doc : _cm.getDocumentContractuelRepository().findAll())
        {
            if(doc.getExternalSource().getOid() == ticketContrat.getIdTicketsContrat())
            {
                return doc;
            }
//            String  trimmedLibelle = ticketContrat.getLibelle().trim();
//            if(trimmedLibelle.equalsIgnoreCase("sans contrat")
//                && doc.getLibelle().trim().equalsIgnoreCase(trimmedLibelle))
//            {
//                return doc;
//            }
        }

        return null;
    }

    private boolean validateTicketContrat(TicketsContrat ticketContrat) {
        if(ticketContrat == null)
            return false;
        if(ticketContrat.getIdTicketsContrat() <= 0)
            return false;
        if(ticketContrat.getReference() == null || ticketContrat.getReference().isEmpty())
            return false;
        return true;
    }
}
