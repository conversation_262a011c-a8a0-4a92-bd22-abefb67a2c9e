package com.helios.etl.services.transformers;

import com.helios.etl.helper.StringHelper;
import com.helios.etl.model.DomaineMetier;
import com.helios.etl.model.IssueStatut;
import com.helios.etl.model.Statut;
import com.helios.etl.services.CacheMemory;
import com.helios.etl.source.entities.Tickets;
import com.helios.etl.utils.TransformationResult;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;

public class StatusToIssueStatutTransformer {
    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(PrioriteToIssuePrioriteTransformer.class);

    @Getter
    @Setter
    private CacheMemory _cm;

    @PersistenceContext
    private EntityManager _em;

    private TransformationResult transformationResult = new TransformationResult();

    public StatusToIssueStatutTransformer(EntityManager em, CacheMemory cm) {
        this._em = em;
        this._cm = cm;
    }

    public IssueStatut transform(Tickets ticket) {
        if (ticket == null) {
            return null;
        }

        IssueStatut issueStatut = null;
        String libelle = ticket.getStatus() == null || ticket.getStatus().isEmpty() ? "Nouveau" : ticket.getStatus();

        for (IssueStatut s : _cm.getIssueStatuts()) {
            if (s.getLibelle().equalsIgnoreCase(libelle)) {
                return s;
            }
        }

        libelle = "HELIOS_V1_" + libelle;
        issueStatut = new IssueStatut();
        issueStatut.setLibelle(getLibelle(ticket));
        issueStatut.setDescription(this.getDescription(ticket));
        issueStatut.setCode("HELIOS_V1_" + libelle);
        issueStatut.setNouveau(false);
        issueStatut.setFerme(true);

        HashSet<DomaineMetier> domaines = new HashSet<>();
        PoleToDomaineMetierTransformer ptdmt = new PoleToDomaineMetierTransformer(_cm);
        DomaineMetier dm = ptdmt.transform("Informatique");
        if(dm != null)
        {
            domaines.add(dm);
        }
        issueStatut.setDomainesMetier(domaines);

        try {
            issueStatut = _cm.getIssueStatutRepository().save(issueStatut);
            _cm.getIssueStatuts().add(issueStatut);
        } catch (Exception e) {
            System.err.println("Failed to save IssueStatut: " + e.getMessage());
            return null;
        }

        return issueStatut;
    }

    private String getDescription(Tickets ticket) {
        String description = "Importé depuis Helios V1.";

        if(ticket == null || ticket.getStatus() == null || ticket.getStatus().isEmpty())
        {
            return description;
        }

        switch (ticket.getStatus().toLowerCase()) {
            case "a planifier":
                description += "\nCe statut représente un ticket qui est transmit à l'équipe planification et est en attente d'être planifié.";
                break;
            case "en cours":
                description += "\nCe statut représente un ticket qui est en cours de traitement.";
                break;
            case "inactif":
                description += "\nCe statut représente un ticket qui est inactif (Généralement un ticket incomplet qui n'a pas été traité).";
                break;
            case "non traité":
                description += "\nCe statut représente un ticket qui n'a pas encore été traité.";
                break;
            case "planifié":
                description += "\nCe statut représente un ticket qui est planifié. Et est en attente d'être traité par le technicien assigné.";
                break;
            case "rappel":
                description += "\nCe statut représente un ticket qui est en attente de rappel pour le client, notamment pour une demande d'information du support ou un rappel commercial.";
                break;
            case "résolu":
                description += "\nCe statut représente un ticket qui est résolu.";
                break;
            case "tnt":
                description += "\nTNT pour 'Travail Non Terminé', est un statut d'intervention pour signaler qu'un autre passage est nécessaire à la résolution";
                break;
            default:
                description += "\nLe statut du ticket n'a pas pu être déterminé.";
                break;
        }

        return description;
    }

    public String getLibelle(Tickets tickets)
    {
        String libelle = "HELIOS_V1_";

        if(tickets == null || tickets.getStatus() == null || tickets.getStatus().isEmpty())
        {
            libelle += "INVALID_STATUS";
            return libelle;
        }

        switch (tickets.getStatus().toLowerCase()) {
            case "a planifier":
                libelle += "A_PLANIFIER";
                break;
            case "en cours":
                libelle += "EN_COURS";
                break;
            case "inactif":
                libelle += "inactif";
                break;
            case "non traité":
                libelle += "NON_TRAITE";
                break;
            case "planifié":
                libelle += "PLANIFIE";
                break;
            case "rappel":
                libelle += "RAPPEL";
                break;
            case "résolu":
                libelle += "RESOLU";
                break;
            case "tnt":
                libelle += "TNT";
                break;
            default:
                libelle += "INVALID_STATUS";
                break;
        }

        return libelle;
    }
}
