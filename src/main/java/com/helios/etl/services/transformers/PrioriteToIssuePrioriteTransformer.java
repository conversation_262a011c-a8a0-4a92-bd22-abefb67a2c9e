package com.helios.etl.services.transformers;

import com.helios.etl.model.DomaineMetier;
import com.helios.etl.model.IssuePriorite;
import com.helios.etl.services.CacheMemory;
import com.helios.etl.source.entities.Tickets;
import com.helios.etl.utils.TransformationResult;
import jakarta.persistence.EntityManager;
import jakarta.persistence.PersistenceContext;
import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;

public class PrioriteToIssuePrioriteTransformer {
    private static final org.slf4j.Logger log = org.slf4j.LoggerFactory.getLogger(PrioriteToIssuePrioriteTransformer.class);

    @Getter
    @Setter
    private CacheMemory _cm;

    @PersistenceContext
    private EntityManager _em;

    private TransformationResult transformationResult = new TransformationResult();

    public PrioriteToIssuePrioriteTransformer(EntityManager em, CacheMemory cm) {
        this._em = em;
        this._cm = cm;
    }

    public IssuePriorite transform(Tickets ticket) {
        if (ticket == null) {
            return null;
        }

        IssuePriorite issuePriorite = null;
        String libelle = ticket.getPriorite() == null || ticket.getPriorite().isEmpty() ? "Normale" : ticket.getPriorite();

        for (IssuePriorite p : _cm.getIssuePriorites()) {
            if (p.getLibelle().equalsIgnoreCase(libelle)) {
                return p;
            }
        }

        libelle = "HELIOS_V1_" + libelle;
        issuePriorite = new IssuePriorite();
        issuePriorite.setLibelle(libelle);
        issuePriorite.setDescription("Imported from Helios V1");
        issuePriorite.setCode("HELIOS_V1_" + libelle);
        issuePriorite.setGrade((byte) 1);
        HashSet<DomaineMetier> domaines = new HashSet<>();
        PoleToDomaineMetierTransformer ptdmt = new PoleToDomaineMetierTransformer(_cm);
        DomaineMetier dm = ptdmt.transform("Informatique");
        if(dm != null)
        {
            domaines.add(dm);
        }
        issuePriorite.setDomainesMetier(domaines);

        try {
            issuePriorite = _cm.getIssuePrioriteRepository().save(issuePriorite);
            _cm.getIssuePriorites().add(issuePriorite);
        } catch (Exception e) {
            System.err.println("Failed to save IssuePriorite: " + e.getMessage());
        }

        return issuePriorite;
    }
}
