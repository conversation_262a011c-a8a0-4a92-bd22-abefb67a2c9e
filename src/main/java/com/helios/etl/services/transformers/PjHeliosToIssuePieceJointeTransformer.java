package com.helios.etl.services.transformers;

import com.helios.etl.model.*;
import com.helios.etl.services.CacheMemory;
import com.helios.etl.source.entities.HeliosPJ;
import lombok.Getter;
import lombok.Setter;

import java.util.HashSet;

public class PjHeliosToIssuePieceJointeTransformer {
    @Getter
    @Setter
    private CacheMemory _cm;

    public PjHeliosToIssuePieceJointeTransformer(CacheMemory cm) {
        this._cm = cm;
    }

    public IssuePieceJointe transform(HeliosPJ pj, AbstractIssue issue, Journal journal) {

        if(pj == null || pj.getNom().trim().isEmpty() || pj.getPath().trim().isEmpty())
        {
            return null;
        }
        if(issue == null || journal == null)
        {
            return null;
        }

        String libelle = pj.getNumero() + " - " + pj.getNom();

        IssuePieceJointe issuePieceJointe = tryGetIssuePieceJointeFromCache(libelle, issue);
        if(issuePieceJointe != null)
        {
            return issuePieceJointe;
        }

        if(pj.getTickets() != null && pj.getTickets().getIdTickets() > 0)
        {
            libelle = "Ticket: " + pj.getTickets().getIdTickets() + " - " + libelle;
        }

        issuePieceJointe = new IssuePieceJointe();
        issuePieceJointe.setLibelle(libelle);
        issuePieceJointe.setDescription(pj.toString());
        issuePieceJointe.setFichier(pj.getNom());
        issuePieceJointe.setIssue(issue);
        issuePieceJointe.setTypeMime(pj.getTypeMime());
        issuePieceJointe.setTaille(pj.getTaille());
        issuePieceJointe.setJournal(journal);

        try {
            issuePieceJointe = _cm.getIssuePieceJointeRepository().save(issuePieceJointe);
            _cm.getIssuePieceJointes().add(issuePieceJointe);
        } catch (Exception e) {
            System.err.println("Failed to save IssuePieceJointe: " + e.getMessage());
            return null;
        }

        return issuePieceJointe;
    }

    public DocumentContractuelPieceJointe transform(HeliosPJ pj, DocumentContractuel document) {
        if(pj == null || pj.getNom().trim().isEmpty() || pj.getPath().trim().isEmpty())
        {
            return null;
        }
        if(document == null)
        {
            return null;
        }

        DocumentContractuelPieceJointe documentPieceJointe = tryGetDocumentContractuelPieceJointeFromCache(pj.getPath(), document);
        if(documentPieceJointe != null)
        {
            return documentPieceJointe;
        }

        String libelle = pj.getNom();
        if(pj.getCommandes() != null && pj.getCommandes().getCommande() != null)
        {
            libelle = "Commande " + pj.getCommandes().getCommande() + " - " + libelle;
        }
        if(pj.getTickets() != null && pj.getTickets().getIdTickets() > 0)
        {
            libelle = "Ticket: " + pj.getTickets().getIdTickets() + " - " + libelle;
        }

        documentPieceJointe = new DocumentContractuelPieceJointe();
        documentPieceJointe.setLibelle(libelle);
        documentPieceJointe.setDescription(pj.toString());
        documentPieceJointe.setFichier(pj.getNom());
        documentPieceJointe.setTypeMime(pj.getTypeMime());
        documentPieceJointe.setTaille(pj.getTaille());
        documentPieceJointe.setDocument(document);

        try {
            documentPieceJointe = _cm.getDocumentContractuelPieceJointeRepository().save(documentPieceJointe);
            _cm.getDocumentContractuelPieceJointes().add(documentPieceJointe);
        } catch (Exception e) {
            System.err.println("Failed to save DocumentContractuelPieceJointe: " + e.getMessage());
            return null;
        }

        return documentPieceJointe;
    }

    private DocumentContractuelPieceJointe tryGetDocumentContractuelPieceJointeFromCache(String path, DocumentContractuel document) {
        if (_cm == null || _cm.getDocumentContractuelPieceJointeRepository() == null) {
            return null;
        }

        DocumentContractuelPieceJointe documentPieceJointe = lookInCollection(_cm.getDocumentContractuelPieceJointes(), path, document);
        if(documentPieceJointe != null)
        {
            return documentPieceJointe;
        }

        HashSet<DocumentContractuelPieceJointe> collection = new HashSet<>(_cm.getDocumentContractuelPieceJointeRepository().findAll());
        documentPieceJointe = lookInCollection(collection, path, document);
        return documentPieceJointe;
    }

    private IssuePieceJointe tryGetIssuePieceJointeFromCache(String libelle, AbstractIssue issue) {
        if (_cm == null || _cm.getIssuePieceJointeRepository() == null) {
            return null;
        }

        IssuePieceJointe issuePieceJointe = lookInCollectionForLibelle(_cm.getIssuePieceJointes(), libelle, issue);
        if(issuePieceJointe != null)
        {
            return issuePieceJointe;
        }

        HashSet<IssuePieceJointe> collection = new HashSet<>(_cm.getIssuePieceJointeRepository().findAll());
        issuePieceJointe = lookInCollectionForLibelle(collection, libelle, issue);
        return issuePieceJointe;
    }

    private IssuePieceJointe lookInCollectionForLibelle(HashSet<IssuePieceJointe> collection, String libelle, AbstractIssue issue) {
        if (collection == null || collection.isEmpty()) {
            return null;
        }

        for (IssuePieceJointe issuePieceJointe : collection) {
            if (issuePieceJointe.getLibelle() != null && libelle != null
                && issuePieceJointe.getLibelle().equalsIgnoreCase(libelle)
                && issuePieceJointe.getIssue() != null
                && issuePieceJointe.getIssue().getOid() == issue.getOid()) {
                return issuePieceJointe;
            }
        }

        return null;
    }

    private DocumentContractuelPieceJointe lookInCollection(HashSet<DocumentContractuelPieceJointe> collection, String pathPj, DocumentContractuel document) {
        if (collection == null || collection.isEmpty()) {
            return null;
        }

        for (DocumentContractuelPieceJointe documentContractuelPieceJointe : collection) {
            if (documentContractuelPieceJointe.getFichier() != null && pathPj != null
                    && documentContractuelPieceJointe.getFichier().equalsIgnoreCase(pathPj)
                    && documentContractuelPieceJointe.getDocument() != null
                    && documentContractuelPieceJointe.getDocument().getOid() == document.getOid()) {
                return documentContractuelPieceJointe;
            }
        }

        return null;
    }
}

