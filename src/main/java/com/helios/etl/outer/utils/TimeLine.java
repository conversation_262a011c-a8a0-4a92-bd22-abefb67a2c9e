/**
 * 
 */
package com.helios.etl.outer.utils;

import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import lombok.Getter;

/**
 * <AUTHOR>
 *
 */
public class TimeLine extends TimeSlot {

	/**
	 * 
	 */
	@Getter
	protected List<TimeSlot> slots = new ArrayList<>();
	
	/**
	 * 
	 */
	public TimeLine() {
		super(timeFromNow(LocalTime.of(0, 0)), 60 *24);
	}
	
	/**
	 * @param start
	 * @param end
	 */
	public TimeLine(LocalTime start, LocalTime end) {
		super(timeFromNow(start), timeFromNow(end));
	}
	
	/**
	 * @param start
	 * @param end
	 * @param meta
	 */
	public TimeLine(LocalTime start, LocalTime end, Object meta) {
		this(timeFromNow(start), timeFromNow(end), meta);
	}
	
	/**
	 * @param start
	 * @param end
	 */
	public TimeLine(LocalDateTime start, LocalDateTime end) {
		super(start, end);
	}
	
	/**
	 * @param start
	 * @param end
	 * @param meta
	 */
	public TimeLine(LocalDateTime start, LocalDateTime end, Object meta) {
		super(start, end, meta);
	}

	/**
	 * @param start
	 * @param duration
	 */
	public TimeLine(LocalTime start, long duration) {
		super(timeFromNow(start), duration);
	}

	/**
	 * @param start
	 * @param duration
	 */
	public TimeLine(LocalDateTime start, long duration) {
		super(start, duration);
	}

	/* (non-Javadoc)
	 * @see fr.actuelburo.utils.TimeSlot#normalize()
	 */
	@Override
	public void normalize() {
		super.normalize();
		
		for(TimeSlot slot : slots) {
			slot.normalize();
		}
	}
	
	/**
	 * @param slot
	 */
	public void add(TimeSlot slot) {
		slots.add(slot);
	}
	
	/**
	 * @param slot
	 */
	public boolean addSafe(TimeSlot slot) {

		if(!contains(slot)) {
			return false;
		}
		
		for(TimeSlot s : slots) {
			if(s.isIntersect(slot)) {
				return false;
			}
		}
		
		slots.add(slot);
		return true;
	}

	/**
	 * @param start
	 * @param end
	 * @return
	 */
	public boolean addSafe(LocalTime start, LocalTime end) {
		TimeSlot slot = new TimeSlot(timeFromNow(start), timeFromNow(end));
		return addSafe(slot);
	}
	
	/**
	 * @param slot
	 */
	public TimeSlot add(LocalTime start, LocalTime end) {
		TimeSlot slot = new TimeSlot(timeFromNow(start), timeFromNow(end));
		add(slot);
		return slot;
	}
	
	/**
	 * @param start
	 * @param end
	 * @return
	 */
	public boolean addSafe(LocalDateTime start, LocalDateTime end) {
		TimeSlot slot = new TimeSlot(start, end);
		return addSafe(slot);
	}
	
	/**
	 * @param slot
	 */
	public TimeSlot add(LocalDateTime start, LocalDateTime end) {
		TimeSlot slot = new TimeSlot(start, end);
		add(slot);
		return slot;
	}
	
	/**
	 * @param slot
	 */
	public void remove(TimeSlot slot) {
		slots.remove(slot);
	}
	
	/**
	 * 
	 */
	public void removeAll() {
		slots.clear();
	}
	
	/**
	 * 
	 */
	public void sort() {
		slots.sort((s1, s2) -> {
			
			if(s1.getStart().isBefore(s2.getStart())) {
				return -1;
			} else if(s1.getStart().isAfter(s2.getStart())){
				return 1;
			} else {
				return 0;
			}
			
		});
	}
	
	/**
	 * @return
	 */
	public int size() {
		return slots.size();
	}
	
	/**
	 * @return
	 */
	public TimeSlot first() {
		return slots.size() > 0 ? slots.get(0) : null;
	}
	
	/**
	 * @return
	 */
	public TimeSlot last() {
		return slots.size() > 0 ? slots.get(slots.size() -1) : null;
	}
	
	/**
	 * @return
	 */
	public long sizeOfFirstGap() {
		
		if(first() != null) {
			return Math.abs(distanceToStart(first().getStart()));
		} else {
			return duration;
		}
	}
	
	/**
	 * @return
	 */
	public long sizeOfLastGap() {
		
		if(last() != null) {
			return Math.abs(distanceToEnd(last().getStart()));
		} else {
			return duration;
		}
	}
	
	/**
	 * @return
	 */
	public boolean contains(TimeSlot slot) {
		return TemporalUtils.isAfterOrEquals(slot.start, start) && TemporalUtils.isBeforeOrEquals(slot.end, end);
	}
	
	/**
	 * @return
	 */
	public TimeSlot clamp(TimeSlot slot) {
		if(TemporalUtils.isAfterOrEquals(start, slot.start)) {
			slot.start = start;
			slot.refreshDuration();
		}
		
		if(TemporalUtils.isBeforeOrEquals(end, slot.end)) {
			slot.end = end;
			slot.refreshDuration();
		}
		
		return slot;
	}
	
	/**
	 * @return
	 */
	public List<TimeSlot> findIntersections(){
		List<TimeSlot> found = new ArrayList<>();
		
		for(TimeSlot slot : slots) {
			
			for(TimeSlot _slot : slots) {
				if(slot.equals(_slot) || found.contains(slot)) {
					continue;
				}
				
				if(slot.isIntersect(_slot)) {
					found.add(_slot);
				}
			}
			
		}
		
		return found;
	}

	/**
	 * @return
	 */
	public List<TimeSlot> removeIntersections(){
		List<TimeSlot> found = findIntersections();
		slots.removeAll(found);
		return found;
	}
	
	/**
	 * @return
	 */
	public boolean hasIntersections() {
		return findIntersections().size() > 0;
	}
	
	/**
	 * @return
	 */
	public List<TimeSlot> findExclusions(){
		List<TimeSlot> found = new ArrayList<>();
		
		for(TimeSlot slot : slots) {
			
			if(!contains(slot)) {
				found.add(slot);
			}
			
		}
		
		return found;
	}

	/**
	 * @return
	 */
	public List<TimeSlot> removeExclusions(){
		List<TimeSlot> found = findExclusions();
		slots.removeAll(found);
		return found;
	}
	
	/**
	 * @return
	 */
	public boolean hasExclusions() {
		return findExclusions().size() > 0;
	}
	
	/**
	 * 
	 */
	public void reduce() {
		
		if(slots.isEmpty()) {
			return;
		}
		
		LocalDateTime _start = null, _end = null;
		
		for(TimeSlot slot : slots) {
			
			if(_start == null) {
				_start = slot.getStart();
				_end = slot.getEnd();
				continue;
			}
			
			if(TemporalUtils.isAfter(_start, slot.start)) {
				_start = slot.start;
			}
			
			if(TemporalUtils.isBefore(_end, slot.end)) {
				_end = slot.end;
			}
		}
		
		reset(_start, _end);
		
	}

	/**
	 * Supprime tous les slots dont la durÃ©e n'est pas exactement Ã©gale Ã  duration
	 * @param duration
	 */
	public void filterByDuration(int duration) {
		
		if(slots.isEmpty()) {
			return;
		}
		
		Iterator<TimeSlot> it = slots.iterator();
		
		while(it.hasNext()) {
			
			TimeSlot slot = it.next();
			if(slot.getDuration() != duration) {
				it.remove();
			}

		}
		
	}

	/**
	 * @param duration
	 */
	public void split(int duration) {
		split(duration, 0);
	}

	/**
	 * @param duration
	 * @param latency
	 */
	public void split(int duration, int latency) {
		TimeSlot slot = null;
		
		if(latency > 0) {
			slots.add(new TimeSlot(start, latency));
		}
		
		do {
			
			if(slot == null) {
				slot = new TimeSlot(start.plusMinutes(latency), duration);
			} else {
				slot.next();
			}
		
			if(contains(slot)) {
				slots.add(slot.duplicate());
			} else {
				
				slot.expandTo(end);
				
				if(slot.duration > 0) {
					slots.add(slot.duplicate());
				} else {
					slot = null;
				}
			}
			
		} while(slot != null);
		
		last().expandTo(end);
		
		normalize();
	}
	
	/**
	 * @return
	 */
	public boolean canFill() {
		return !hasIntersections() && !hasExclusions();
	}
	
	/**
	 * @return
	 */
	public boolean needFill() {
		
		if(slots.size() == 0) {
			return true;
		}
		
		// La liste doit Ãªtre triÃ©e
		sort();
		
		if(slots.get(0).distanceToStart(start) != 0) {
			return true;
		}
		
		for(int i = 0; i < slots.size(); i++) {
			
			// C'est le dernier
			if(slots.size() == i+1) {
				break;
			}
			
			if(slots.get(i).distance(slots.get(i +1)) > 1) {
				return false;
			}
		}
		
		if(last().distanceToEnd(end) != 0) {
			return true;
		}
		
		return false;
		
	}
	
	/**
	 * 
	 */
	public boolean fill() {
		return fill(0);
	}
	
	/**
	 * @param duration
	 */
	public void fillIfEmpty(int duration) {
		
		if(!slots.isEmpty()) {
			return;
		}
		
		if (duration <= 0) {
			slots.add(new TimeSlot(start, this.duration));
			
		} else {
			LocalDateTime _start = start;
			LocalDateTime _end = _start.plusMinutes(duration-1);
			
			while (_end.compareTo(this.end) <= 0) {
				slots.add(new TimeSlot(_start, _end));
				_start = _end.plusMinutes(1);
				_end = _start.plusMinutes(duration-1);
			}		
		}

	}
	
	/**
	 * @param start
	 * @param end
	 * @param duration
	 * @return
	 */
	public static List<TimeSlot> splitInterval(LocalDateTime start, LocalDateTime end, int duration) {
		TimeLine timeLine = new TimeLine(start, end);
		timeLine.fillIfEmpty(duration);
		return timeLine.slots;
	}

	/**
	 * @param start
	 * @param end
	 * @param duration
	 * @param slots
	 * @return
	 */
	protected TimeSlot splitInterval(LocalDateTime start, LocalDateTime end, int duration, List<TimeSlot> slots){
		
		List<TimeSlot> _slots = splitInterval(start, end, duration);
		slots.addAll(_slots);
		
		return _slots.get(_slots.size() -1);
		
	}
	
	/**
	 * @param duration
	 * @return
	 */
	public boolean fill(int duration) {

		if(!canFill()) {
			return false;
		}
		
		// Aucun slot, on ajoutes les slot manquant
		if(slots.isEmpty()) {
			fillIfEmpty(duration);
			return true;
		}
		
		// La liste doit Ãªtre triÃ©e
		sort();
		
		// Ici on place les slots crÃ©Ã©es
		List<TimeSlot> _slots = new ArrayList<>();
		
		TimeSlot first = slots.get(0),
				slot = null, 
				next = null;
		int i = 0;
		
		// On recherche le point de dÃ©part
		if(first.start.equals(start)) {
			
			slot = first;
			i = 1;
			
		} else {
			
			if (duration > 0) {
				
				// s'il y a au moins duration entre le dÃ©but de la timeline et le dÃ©but du premier slot
				// on dÃ©coupe cet espace en slot
				if(start.until(slots.get(0).start, ChronoUnit.MINUTES) >= duration) {
					
					TimeLine _timeLine = new TimeLine(start, slots.get(0).start.minusMinutes(1));
					_timeLine.fillIfEmpty(duration);
					_slots.addAll(_timeLine.slots);
					slot = _timeLine.slots.get(_timeLine.slots.size() -1);
					
				} else {
					slot = first;
				}
				
			} else {
				slot = new TimeSlot(start, slots.get(0).start.minusMinutes(1));
				_slots.add(slot);
			}
			
		}
		
		// Ils sont tous triÃ©s, on passe en revue tous les slots
		// et on ajoute un slot par trou
		for(; i < slots.size(); i++) {
			next = slots.get(i);

			if(slot.distance(next) > 1) {
				if (duration > 0) {
					
					TimeSlot _slotNearest = new TimeSlot(start, duration);
					_slotNearest.closestStartAfter(slot.end.plusMinutes(1));
					
					TimeLine _timeLine = new TimeLine(_slotNearest.start, next.start.minusMinutes(1));
					_timeLine.fillIfEmpty(duration);
					_slots.addAll(_timeLine.slots);
					
				} else {
					slot = new TimeSlot(slot.end.plusMinutes(1), next.start.minusMinutes(1));
					_slots.add(slot);					
				}
			}
			
			slot = next;
		
		}
		
		// On vÃ©rifie s'il ne reste un trou Ã  la fin
		if(Math.abs(slot.distanceToEnd(end)) > 0) {
			if (duration > 0) {
				
				TimeSlot _slotNearest = new TimeSlot(start,duration);
				_slotNearest.closestStartAfter(slot.end.plusMinutes(1));
				
				TimeLine _timeLine = new TimeLine(_slotNearest.start, end);
				_timeLine.fillIfEmpty(duration);
				_slots.addAll(_timeLine.slots);
				
			} else {
				slot = new TimeSlot(slot.end.plusMinutes(1), end);
				_slots.add(slot);
			}
		}
		
		// On ajoute tous ceux qui ont Ã©tÃ© crÃ©Ã©s et on trie la liste
		slots.addAll(_slots);
		sort();
		
		return true;
	}
	
	/**
	 * Permet de combler les trous en considÃ©rant une marge avant et aprÃ¨s chaque slot existant
	 * @param slotMarginLeft la marge avant les slots existant
	 * @param slotMarginRight la marge aprÃ¨s les slots existant
	 * @param timeLineMarginLeft la marge au dÃ©but de la timeline
	 * @param timeLineMarginRight la marge Ã  la fin de la timeline
	 * @param duration la durÃ©e de chaque nouveau slot crÃ©Ã©, si la valeur est 0, chaque slot crÃ©Ã© prends l'espace maximum
	 * @return
	 */
	public boolean fill(int slotMarginLeft, int slotMarginRight, int timeLineMarginLeft, int timeLineMarginRight, int duration) {

		// 0- Phase 0 : ContrÃ´le et prÃ©paration des donnÃ©es
		if(!canFill()) {
			return false;
		}
		
		// Aucun slot, on remplis la timeline
		if(slots.isEmpty()) {
			fillIfEmpty(duration);
			return true;
		}
		
		// on enlÃ¨ve ici les marges de la timeline, plus simple car cela revient Ã  une timeline plus courte
		LocalDateTime tlStart = start.plusMinutes(timeLineMarginLeft);
		LocalDateTime tlEnd = end.minusMinutes(timeLineMarginRight);
		
		// La liste doit Ãªtre triÃ©e
		sort();
		
		// Ici on placera les slots crÃ©Ã©es
		List<TimeSlot> _slots = new ArrayList<>();
		
		// 1- Phase 1 : On dÃ©termine le point de dÃ©part
		// Indique le slot existant Ã  considÃ©rer comme point de dÃ©part lors de la phase 2
		
		TimeSlot first = first(), // le premier slot Ã  traiter
				slot = null, // le slot courant
				next = null; // le prochain slot existant
		
		// si un slot existe au tout dÃ©but de la timeline, slotsIndex sera Ã©gale alors Ã  1
		int slotsIndex = 0;
		
		// On recherche le point de dÃ©part
		if(first.distanceToStart(tlStart) <= 0) {
			
			slot = first;
			slotsIndex = 1;
			
		} else if (slotMarginLeft > 0) {
			
			// s'il y a au moins margin entre le dÃ©but de la timeline 
			// et le dÃ©but du premier slot, on dÃ©coupe cet espace en slot de durÃ©e duration
			// en considÃ©rant la marge avant le premier slot existant
			if(tlStart.until(slots.get(0).start, ChronoUnit.MINUTES) > slotMarginLeft) {
				
				slot = splitInterval(
					tlStart,
					slots.get(0).start.minusMinutes(slotMarginLeft +1),
					duration,
					_slots	
				);
				
			} else {
				slot = first;
			}
			
		} else {
			
			slot = splitInterval(
				tlStart,
				first.start.minusMinutes(1),
				duration,
				_slots	
			);
			
		}
		
		// 2- Phase 2 : On passe en revue les slots existants
		// On passe en revue tous les slots pour combler les espaces vides
		for(; slotsIndex < slots.size(); slotsIndex++) {
			
			// le prochain slot existant
			next = slots.get(slotsIndex);
			
			// indique si le slot courant est un solt existant avant remplissage
			boolean slotExists = slots.contains(slot);
			
			// ne pas traiter les slots adjacents
			if(slot.distance(next) > 1) {
				
				// s'il y a des marges
				// sinon on remplis direcetement l'espace vide
				if(slotMarginLeft > 0 || slotMarginRight > 0){
					
					// si c'est un slot existant, il faut considÃ©rer les marges avant le slot suivant et aprÃ¨s ce slot
					// si c'est un slot ajoutÃ©, on ne considÃ¨re que la marge avant le prochain slot
					// sinon, il n'y a pas la place, on passe directement au suivant
					if(slotExists && slot.distance(next) > slotMarginLeft + slotMarginRight +1) {

						slot = splitInterval(
							slot.end.plusMinutes(slotMarginRight +1),
							next.start.minusMinutes(slotMarginRight +1),
							duration,
							_slots	
						);
							
					} else if(!slotExists && slot.distance(next) > slotMarginLeft +1) {
						
						slot = splitInterval(
							slot.end.plusMinutes(1),
							next.start.minusMinutes(slotMarginLeft +1),
							duration,
							_slots	
						);
							
					} else {
						slot = next;
					}
		
				} else {
					
					slot = splitInterval(
						slot.end.plusMinutes(1),
						next.start.minusMinutes(1),
						duration,
						_slots	
					);
					
				}
				
			}
			
			slot = next;
		
		}
		
		// 3- phase 3 : Prise en compte de la fin de la timeline
		// On vÃ©rifie s'il reste un trou Ã  la fin
		if(Math.abs(slot.distanceToEnd(tlEnd)) > 0) {
			
			// indique si le slot courant est un solt existant avant remplissage
			boolean slotExists = slots.contains(slot);
			
			if(slotMarginRight > 0){
				
				if(slot.end.until(tlEnd, ChronoUnit.MINUTES) > slotMarginRight) {
					
					int minutes = 1;
					
					if(slotExists) {
						minutes += slotMarginRight;
					}
					
					slot = splitInterval(
						slot.end.plusMinutes(minutes),
						tlEnd,
						duration,
						_slots	
					);
					
				}
	
			} else if(slot.end.until(tlEnd, ChronoUnit.MINUTES) > 0){
				
				slot = splitInterval(
					slot.end.plusMinutes(1),
					tlEnd,
					duration,
					_slots	
				);
			}
		}
		
		// 4- Phase 4 : finalisation
		// On ajoute tous ceux qui ont Ã©tÃ© crÃ©Ã©s et on trie la liste
		slots.addAll(_slots);
		sort();
		
		return true;
	}
	
	/* (non-Javadoc)
	 * @see fr.actuelburo.utils.TimeSlot#toString()
	 */
	@Override
	public String toString() {
		return String.format(
			"TimeLine %s -> %s (%d)", 
			start.toString().replace("T", " "), 
			end.toString().replace("T", " "), 
			duration
		);
	}
	
	/**
	 * @return
	 */
	public String allToString() {
		StringBuilder sb = new StringBuilder();
		sb.append(toString()).append("\n");
		
		for(TimeSlot slot : slots) {
			sb.append("\t").append(slot.toString()).append("\n");
		}
		
		return sb.toString();
	}

}


