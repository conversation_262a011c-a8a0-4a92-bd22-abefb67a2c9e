package com.helios.etl.helper;

import java.text.Normalizer;

public class StringHelper {
    // Helper method for null/empty string validation
    public static boolean isNullOrEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    public static String removeAccents(String input) {
        if (input == null) {
            return null;
        }

        String normalized = Normalizer.normalize(input, Normalizer.Form.NFD);
        return normalized.replaceAll("\\p{InCombiningDiacriticalMarks}+", "");
    }

    /**
     * Generate a unique code based on two strings
     * @param s first string, can be of any value, cannot be null
     * @param s2 second string, can be of any value, including null
     * @return a likely unique code string
     */
    public static String generateUniqueCode(String s, String s2) {
        // Clean and normalize the inputs
        String cleanS = s != null ? s.replaceAll("[^a-zA-Z0-9]", "").toUpperCase() : "UNKNOWN";
        String cleanS2 = s2 != null ? s2.replaceAll("[^a-zA-Z0-9]", "").toUpperCase() : "UNKNOWN";

        // Limit length to avoid exceeding database column constraints (50 chars max)
        if (cleanS.length() > 10) cleanS = cleanS.substring(0, 15);
        if (cleanS2.length() > 15) cleanS2 = cleanS2.substring(0, 15);

        // Generate base code RC = RandomCode
        String baseCode = "RC_" + cleanS;

        if(cleanS2 != null && !cleanS2.isEmpty()) {
            baseCode += "_" + cleanS2;
        }

        baseCode += "_" + System.currentTimeMillis() % 100000;

        // Ensure it doesn't exceed 50 characters
        if (baseCode.length() > 45) {
            baseCode = baseCode.substring(0, 45);
        }


        return baseCode;
    }

    public static String truncate(String text, int i) {
        if (text.length() > i) {
            text = text.substring(0, i);
        }
        return text;
    }

    /**
     * Helper method to sanitize a single string field
     * @param getter Function to get the current field value
     * @param setter Function to set the sanitized field value
     * @param maxLength Maximum allowed length for the field
     */
    public static void sanitizeField(java.util.function.Supplier<String> getter,
                                      java.util.function.Consumer<String> setter,
                                      int maxLength, String defaultValue) {
        String value = getter.get() != null ? getter.get() : defaultValue;
        if (value != null) {
            // Truncate if too long, then always trim
            if (value.length() > maxLength) {
                value = truncate(value, maxLength);
            }
            setter.accept(value.trim());
        }
    }
}
