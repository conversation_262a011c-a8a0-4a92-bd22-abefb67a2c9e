package com.helios.etl.source.repositories;

import com.helios.etl.source.entities.TicketsContrat;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.RowMapper;
import org.springframework.stereotype.Repository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.List;

@Repository
public class TicketsContratRepository {

    private final JdbcTemplate jdbcTemplate;

    @Autowired
    public TicketsContratRepository(@Qualifier("sourceSqlServerJdbcTemplate") JdbcTemplate jdbcTemplate) {
        this.jdbcTemplate = jdbcTemplate;
    }

    private final RowMapper<TicketsContrat> rowMapper = new RowMapper<TicketsContrat>() {
        @Override
        public TicketsContrat mapRow(ResultSet rs, int rowNum) throws SQLException {
            TicketsContrat tc = new TicketsContrat();
            tc.setIdTicketsContrat(rs.getInt("idTicketsContrat"));
            tc.setIdTickets(rs.getInt("idTickets"));
            tc.setReference(rs.getString("reference"));
            tc.setLibelle(rs.getString("libelle"));
            return tc;
        }
    };

    /**
     * Retrieves all TicketsContrat records
     *
     * @return List of all TicketsContrat
     */
    public List<TicketsContrat> getAll() {
        String sql = "SELECT * FROM TicketsContrat ORDER BY idTicketsContrat DESC";
        return jdbcTemplate.query(sql, rowMapper);
    }

    /**
     * Retrieves all TicketsContrat records for a specific ticket
     *
     * @param idTickets the ticket ID to filter by
     * @return List of TicketsContrat for the specified ticket
     */
    public List<TicketsContrat> getByTicketId(int idTickets) {
        String sql = "SELECT * FROM TicketsContrat WHERE idTickets = ? ORDER BY idTicketsContrat DESC";
        return jdbcTemplate.query(sql, rowMapper, idTickets);
    }

    public TicketsContrat getById(int id) {
        String sql = "SELECT * FROM TicketsContrat WHERE idTicketsContrat = ?";
        List<TicketsContrat> result = jdbcTemplate.query(sql, rowMapper, id);
        return result.isEmpty() ? null : result.get(0);
    }

    public void add(TicketsContrat entity) {
        String sql = "INSERT INTO TicketsContrat (idTickets, reference, libelle) VALUES (?, ?, ?)";
        jdbcTemplate.update(sql,
                entity.getIdTickets(),
                entity.getReference(),
                entity.getLibelle()
        );
    }

    public void update(TicketsContrat entity) {
        String sql = "UPDATE TicketsContrat SET idTickets = ?, reference = ?, libelle = ? WHERE idTicketsContrat = ?";
        jdbcTemplate.update(sql,
                entity.getIdTickets(),
                entity.getReference(),
                entity.getLibelle(),
                entity.getIdTicketsContrat()
        );
    }

    public void delete(TicketsContrat entity) {
        String sql = "DELETE FROM TicketsContrat WHERE idTicketsContrat = ?";
        jdbcTemplate.update(sql, entity.getIdTicketsContrat());
    }
}