package com.helios.etl.source.entities;

import com.helios.etl.source.helper.DefaultHelper;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * Représente l'historique d'un ticket dans le système Helios V1
 * Ne représente pas une table de la base de données, il s'agit d'une structure de données intermédiaire
 */
@Data
public class TicketsHistorique {
    private int idHistorique;
    private int idTickets;
    private LocalDateTime dateModification = DefaultHelper.DEFAULT_MIN_DATE;
    private String correspondant = "";
    private String description = "";
    private int noteInterne;
    private int pieceJointe;
    private int envoiEmail;
    private int noteType;
    private int temps;
}