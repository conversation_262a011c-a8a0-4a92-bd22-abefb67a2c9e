package com.helios.etl.source.helper;

import com.helios.etl.model.AbstractIssue;

public class DataOptionsHelper {
    public static Integer getIdTicketsFromIssueOptions(AbstractIssue issue) {
        if (issue.getOptions() == null) {
            return null;
        }

        String idTicketsStr = issue.getOptions().get("heliosv1.idTickets");
        if (idTicketsStr != null && !idTicketsStr.isEmpty()) {
            try {
                return Integer.parseInt(idTicketsStr);
            } catch (NumberFormatException e) {
                System.err.println("Failed to parse idTickets: " + idTicketsStr);
                return null;
            }
        }

        return null;
    }

}
